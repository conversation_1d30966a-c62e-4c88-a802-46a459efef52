// Dashboard utility functions and types

export interface UserStats {
  savedProperties: number
  viewedProperties: number
  inquiries: number
  alerts: number
}

export interface ActivityItem {
  id: number
  type: 'saved' | 'inquiry' | 'viewed'
  title: string
  location: string
  price: string
  time: string
}

export interface QuickAction {
  title: string
  description: string
  href: string
  icon: unknown // React component
  color: string
}

// Mock data functions - in a real app, these would fetch from your backend
export const getUserStats = async (userId: string): Promise<UserStats> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 100))

  return {
    savedProperties: 12,
    viewedProperties: 45,
    inquiries: 8,
    alerts: 3
  }
}

export const getRecentActivity = async (userId: string): Promise<ActivityItem[]> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 100))

  return [
    {
      id: 1,
      type: 'saved',
      title: 'Luxury Condo in BGC',
      location: 'Bonifacio Global City',
      price: '₱15,000,000',
      time: '2 hours ago'
    },
    {
      id: 2,
      type: 'inquiry',
      title: 'Modern House in Alabang',
      location: 'Muntinlupa City',
      price: '₱8,500,000',
      time: '1 day ago'
    },
    {
      id: 3,
      type: 'viewed',
      title: 'Penthouse in Makati',
      location: 'Makati City',
      price: '₱25,000,000',
      time: '2 days ago'
    }
  ]
}

export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency: 'PHP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price)
}

export const formatTimeAgo = (date: Date): string => {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Just now'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days} day${days > 1 ? 's' : ''} ago`
  }
}
