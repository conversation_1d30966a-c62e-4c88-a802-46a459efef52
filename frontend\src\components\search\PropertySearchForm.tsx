'use client'

import { useState } from 'react'
import LocationSearch from './LocationSearch'
import PropertyTypeSelector from './PropertyTypeSelector'
import PriceRangeFilter from './PriceRangeFilter'
import SearchButton from './SearchButton'

export interface SearchFilters {
  location: string
  propertyType: string
  priceRange: string
  listingType: 'sale' | 'rent'
  bedrooms?: string
  bathrooms?: string
  minArea?: string
  maxArea?: string
}

interface PropertySearchFormProps {
  onSearch: (filters: SearchFilters) => void
  initialFilters?: Partial<SearchFilters>
  layout?: 'horizontal' | 'vertical' | 'compact'
  showAdvanced?: boolean
  className?: string
}

const PropertySearchForm = ({
  onSearch,
  initialFilters = {},
  layout = 'horizontal',
  showAdvanced = false,
  className = ''
}: PropertySearchFormProps) => {
  const [filters, setFilters] = useState<SearchFilters>({
    location: '',
    propertyType: '',
    priceRange: '',
    listingType: 'sale',
    bedrooms: '',
    bathrooms: '',
    minArea: '',
    maxArea: '',
    ...initialFilters
  })

  const [isLoading, setIsLoading] = useState(false)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(showAdvanced)

  const updateFilter = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleSearch = async () => {
    setIsLoading(true)
    try {
      await onSearch(filters)
    } finally {
      setIsLoading(false)
    }
  }

  const clearFilters = () => {
    setFilters({
      location: '',
      propertyType: '',
      priceRange: '',
      listingType: 'sale',
      bedrooms: '',
      bathrooms: '',
      minArea: '',
      maxArea: ''
    })
  }

  const bedroomOptions = [
    { value: '', label: 'Any Bedrooms' },
    { value: 'studio', label: 'Studio' },
    { value: '1', label: '1 Bedroom' },
    { value: '2', label: '2 Bedrooms' },
    { value: '3', label: '3 Bedrooms' },
    { value: '4', label: '4 Bedrooms' },
    { value: '5+', label: '5+ Bedrooms' }
  ]

  const bathroomOptions = [
    { value: '', label: 'Any Bathrooms' },
    { value: '1', label: '1 Bathroom' },
    { value: '2', label: '2 Bathrooms' },
    { value: '3', label: '3 Bathrooms' },
    { value: '4', label: '4 Bathrooms' },
    { value: '5+', label: '5+ Bathrooms' }
  ]

  const layoutClasses = {
    horizontal: 'grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4',
    vertical: 'space-y-4',
    compact: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3'
  }

  return (
    <div className={`bg-white rounded-2xl shadow-2xl p-6 sm:p-8 ${className}`}>
      {/* Listing Type Toggle */}
      <div className="mb-6">
        <div className="flex bg-gray-100 rounded-lg p-1 w-fit">
          <button
            onClick={() => updateFilter('listingType', 'sale')}
            className={`px-6 py-2 rounded-md font-semibold transition-all duration-200 ${
              filters.listingType === 'sale' ? 'bg-lime-600 text-white shadow-md' : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            For Sale
          </button>
          <button
            onClick={() => updateFilter('listingType', 'rent')}
            className={`px-6 py-2 rounded-md font-semibold transition-all duration-200 ${
              filters.listingType === 'rent' ? 'bg-lime-600 text-white shadow-md' : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            For Rent
          </button>
        </div>
      </div>

      {/* Main Search Fields */}
      <div className={layoutClasses[layout]}>
        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-700">Location</label>
          <LocationSearch
            value={filters.location}
            onChange={value => updateFilter('location', value)}
            placeholder="Enter city, area, or address"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-700">Property Type</label>
          <PropertyTypeSelector value={filters.propertyType} onChange={value => updateFilter('propertyType', value)} />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-700">Price Range</label>
          <PriceRangeFilter
            value={filters.priceRange}
            onChange={value => updateFilter('priceRange', value)}
            listingType={filters.listingType}
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-700">&nbsp;</label>
          <SearchButton onClick={handleSearch} loading={isLoading} fullWidth={layout !== 'horizontal'} size="lg" />
        </div>
      </div>

      {/* Advanced Filters Toggle */}
      <div className="mt-6">
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="text-lime-600 hover:text-lime-700 font-medium text-sm flex items-center gap-2"
        >
          <span>{showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters</span>
          <svg
            className={`w-4 h-4 transition-transform duration-200 ${showAdvancedFilters ? 'rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Bedrooms</label>
              <select
                value={filters.bedrooms}
                onChange={e => updateFilter('bedrooms', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent"
              >
                {bedroomOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Bathrooms</label>
              <select
                value={filters.bathrooms}
                onChange={e => updateFilter('bathrooms', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent"
              >
                {bathroomOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Min Area (sqm)</label>
              <input
                type="number"
                value={filters.minArea}
                onChange={e => updateFilter('minArea', e.target.value)}
                placeholder="Min area"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Max Area (sqm)</label>
              <input
                type="number"
                value={filters.maxArea}
                onChange={e => updateFilter('maxArea', e.target.value)}
                placeholder="Max area"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <button onClick={clearFilters} className="text-gray-600 hover:text-gray-800 font-medium text-sm">
              Clear All Filters
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default PropertySearchForm
