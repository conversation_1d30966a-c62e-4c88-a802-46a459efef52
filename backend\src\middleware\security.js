import rateLimit from 'express-rate-limit'
import slowDown from 'express-slow-down'
import validator from 'validator'
import logger from '../utils/logger.js'

/**
 * Enhanced rate limiting with different limits for different endpoints
 */
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}, URL: ${req.originalUrl}`)
      res.status(429).json({ error: message })
    }
  })
}

// General API rate limiter
const generalLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  'Too many requests from this IP, please try again later.'
)

// Strict rate limiter for authentication endpoints
const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  5,
  'Too many authentication attempts, please try again later.'
)

// Very strict rate limiter for password reset
const passwordResetLimiter = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  3,
  'Too many password reset attempts, please try again later.'
)

// Moderate rate limiter for user management operations
const userManagementLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  50,
  'Too many user management requests, please try again later.'
)

/**
 * Slow down middleware for progressive delays
 */
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per 15 minutes, then...
  delayMs: () => 500, // begin adding 500ms of delay per request above 50
  maxDelayMs: 20000, // maximum delay of 20 seconds
  validate: { delayMs: false } // disable the warning
})

/**
 * Input sanitization middleware
 */
const sanitizeInput = (req, res, next) => {
  try {
    // Sanitize string inputs
    const sanitizeObject = obj => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          // Remove potential XSS attempts
          obj[key] = validator.escape(obj[key])
          // Trim whitespace
          obj[key] = obj[key].trim()
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          sanitizeObject(obj[key])
        }
      }
    }

    if (req.body && typeof req.body === 'object') {
      sanitizeObject(req.body)
    }

    if (req.query && typeof req.query === 'object') {
      sanitizeObject(req.query)
    }

    next()
  } catch (error) {
    logger.error('Input sanitization error:', error)
    res.status(400).json({
      success: false,
      error: 'Invalid input format'
    })
  }
}

/**
 * Request size limiter
 */
const requestSizeLimiter = (req, res, next) => {
  const contentLength = parseInt(req.get('content-length'))
  const maxSize = parseInt(process.env.MAX_REQUEST_SIZE) || 10 * 1024 * 1024 // 10MB default

  if (contentLength && contentLength > maxSize) {
    logger.warn(`Request too large: ${contentLength} bytes from IP: ${req.ip}`)
    return res.status(413).json({
      success: false,
      error: 'Request entity too large'
    })
  }

  next()
}

/**
 * Security headers middleware (additional to helmet)
 */
const securityHeaders = (req, res, next) => {
  // Additional security headers
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin')
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()')

  // Remove server information
  res.removeHeader('X-Powered-By')

  next()
}

/**
 * IP whitelist/blacklist middleware
 */
const ipFilter = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress

  // Check if IP is blacklisted
  const blacklistedIPs = process.env.BLACKLISTED_IPS ? process.env.BLACKLISTED_IPS.split(',') : []
  if (blacklistedIPs.includes(clientIP)) {
    logger.warn(`Blocked request from blacklisted IP: ${clientIP}`)
    return res.status(403).json({
      success: false,
      error: 'Access denied'
    })
  }

  // Check if whitelist is enabled and IP is not whitelisted
  const whitelistedIPs = process.env.WHITELISTED_IPS ? process.env.WHITELISTED_IPS.split(',') : []
  if (whitelistedIPs.length > 0 && !whitelistedIPs.includes(clientIP)) {
    logger.warn(`Blocked request from non-whitelisted IP: ${clientIP}`)
    return res.status(403).json({
      success: false,
      error: 'Access denied'
    })
  }

  next()
}

/**
 * Request logging middleware for security monitoring
 */
const securityLogger = (req, res, next) => {
  const startTime = Date.now()

  // Log suspicious patterns
  const suspiciousPatterns = [
    /(\<script\>|\<\/script\>)/i,
    /(union.*select|select.*from|insert.*into|delete.*from|drop.*table)/i,
    /(\.\.\/|\.\.\\)/,
    /(\%27|\%22|\%3C|\%3E)/i
  ]

  const requestData = JSON.stringify({
    body: req.body,
    query: req.query,
    params: req.params
  })

  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(requestData))

  if (isSuspicious) {
    logger.warn(`Suspicious request detected from IP: ${req.ip}, URL: ${req.originalUrl}, Data: ${requestData}`)
  }

  // Log response time and status
  res.on('finish', () => {
    const duration = Date.now() - startTime
    if (duration > 5000) {
      // Log slow requests
      logger.warn(`Slow request: ${req.method} ${req.originalUrl} took ${duration}ms`)
    }
  })

  next()
}

/**
 * CORS configuration with enhanced security
 */
const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = process.env.ALLOWED_ORIGINS
      ? process.env.ALLOWED_ORIGINS.split(',')
      : ['http://localhost:3000']

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true)

    if (allowedOrigins.includes(origin)) {
      callback(null, true)
    } else {
      logger.warn(`CORS blocked request from origin: ${origin}`)
      callback(new Error('Not allowed by CORS'))
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count'],
  maxAge: 86400 // 24 hours
}

/**
 * Content Security Policy
 */
const cspOptions = {
  directives: {
    defaultSrc: ["'self'"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    scriptSrc: ["'self'"],
    imgSrc: ["'self'", 'data:', 'https:'],
    connectSrc: ["'self'"],
    fontSrc: ["'self'"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"]
  }
}

export {
  generalLimiter,
  authLimiter,
  passwordResetLimiter,
  userManagementLimiter,
  speedLimiter,
  sanitizeInput,
  requestSizeLimiter,
  securityHeaders,
  ipFilter,
  securityLogger,
  corsOptions,
  cspOptions
}
