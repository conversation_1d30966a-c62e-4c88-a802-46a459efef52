# Dashboard Components

This directory contains reusable components for the user dashboard interface.

## Components

### StatsCard
A card component for displaying key statistics with an icon.

**Props:**
- `title` (string): The label for the statistic
- `value` (string | number): The statistic value to display
- `icon` (ReactNode): The icon to display
- `iconColor` (string, optional): Tailwind color class for the icon

**Example:**
```tsx
<StatsCard
  title="Saved Properties"
  value={12}
  icon={<HeartIcon className="h-8 w-8" />}
  iconColor="text-red-500"
/>
```

### QuickActionCard
A clickable card component for quick actions/navigation.

**Props:**
- `title` (string): The action title
- `description` (string): Brief description of the action
- `href` (string): The link destination
- `icon` (ReactNode): The icon to display
- `color` (string, optional): Tailwind background color classes for the icon container

**Example:**
```tsx
<QuickActionCard
  title="Search Properties"
  description="Find your dream property"
  href="/properties"
  icon={<HomeIcon className="h-6 w-6" />}
  color="bg-lime-500 hover:bg-lime-600"
/>
```

### ActivityItem
A component for displaying recent activity items.

**Props:**
- `title` (string): The activity title
- `location` (string): Property location
- `price` (string): Property price
- `time` (string): Time of activity
- `icon` (ReactNode): The activity type icon
- `iconColor` (string, optional): Tailwind color class for the icon

**Example:**
```tsx
<ActivityItem
  title="Luxury Condo in BGC"
  location="Bonifacio Global City"
  price="₱15,000,000"
  time="2 hours ago"
  icon={<HeartIcon className="h-6 w-6" />}
  iconColor="text-red-500"
/>
```

## Features

- **Responsive Design**: All components are fully responsive using Tailwind CSS v4+
- **Accessibility**: Proper ARIA labels, semantic HTML, and keyboard navigation support
- **Consistent Styling**: Follows the application's design system and color palette
- **Hover Effects**: Smooth transitions and hover states for better user experience

## Usage

Import components from the index file:

```tsx
import { StatsCard, QuickActionCard, ActivityItem } from '@/components/dashboard'
```

## Testing

Test files are located in the `__tests__` directory. Run tests with:

```bash
npm test
```
