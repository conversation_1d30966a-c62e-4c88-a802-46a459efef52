import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { authenticate } from '../middleware/auth.js'
import { checkPermission } from '../middleware/authorization.js'
import prisma from '../utils/database.js'
import logger from '../utils/logger.js'

const router = express.Router()

// Apply authentication to all routes
router.use(authenticate)

// Validation rules
const createModuleValidation = [
  body('name').isLength({ min: 1, max: 100 }).trim().withMessage('Module name must be 1-100 characters'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

const updateModuleValidation = [
  param('id').isString().withMessage('Module ID must be a string'),
  body('name').optional().isLength({ min: 1, max: 100 }).trim().withMessage('Module name must be 1-100 characters'),
  body('description').optional().isLength({ max: 500 }).trim().withMessage('Description must be max 500 characters'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
]

/**
 * @route   GET /api/modules
 * @desc    Get all modules with pagination and filtering
 * @access  Private (requires Modules:read permission)
 */
router.get('/', checkPermission('Modules', 'read'), async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const search = req.query.search || ''
    const isActive = req.query.isActive

    const skip = (page - 1) * limit

    // Build where clause
    const where = {}

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    const [modules, total] = await Promise.all([
      prisma.module.findMany({
        where,
        include: {
          permissions: {
            include: {
              roles: {
                include: {
                  role: {
                    select: {
                      id: true,
                      name: true,
                      description: true
                    }
                  }
                }
              }
            }
          },
          _count: {
            select: {
              permissions: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.module.count({ where })
    ])

    res.json({
      success: true,
      data: {
        modules,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    logger.error('Get modules error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get modules'
    })
  }
})

/**
 * @route   GET /api/modules/:id
 * @desc    Get module by ID
 * @access  Private (requires Modules:read permission)
 */
router.get('/:id', checkPermission('Modules', 'read'), async (req, res) => {
  try {
    const { id } = req.params

    const module = await prisma.module.findUnique({
      where: { id },
      include: {
        permissions: {
          include: {
            roles: {
              include: {
                role: {
                  select: {
                    id: true,
                    name: true,
                    description: true,
                    isActive: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!module) {
      return res.status(404).json({
        success: false,
        error: 'Module not found'
      })
    }

    res.json({
      success: true,
      data: { module }
    })
  } catch (error) {
    logger.error('Get module error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get module'
    })
  }
})

/**
 * @route   POST /api/modules
 * @desc    Create new module
 * @access  Private (requires Modules:create permission)
 */
router.post('/', checkPermission('Modules', 'create'), createModuleValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { name, description, isActive = true } = req.body

    // Check if module already exists
    const existingModule = await prisma.module.findUnique({
      where: { name }
    })

    if (existingModule) {
      return res.status(400).json({
        success: false,
        error: 'Module with this name already exists'
      })
    }

    // Create module
    const module = await prisma.module.create({
      data: {
        name,
        description: description || null,
        isActive
      }
    })

    // Create default permissions for the module
    const defaultActions = ['create', 'read', 'update', 'delete']
    const permissions = await Promise.all(
      defaultActions.map(action =>
        prisma.permission.create({
          data: {
            action,
            description: `${action.charAt(0).toUpperCase() + action.slice(1)} ${name.toLowerCase()}`,
            moduleId: module.id
          }
        })
      )
    )

    logger.info(`Module created by ${req.user.email}: ${module.name} with ${permissions.length} default permissions`)

    res.status(201).json({
      success: true,
      message: 'Module created successfully',
      data: {
        module: {
          ...module,
          permissions
        }
      }
    })
  } catch (error) {
    logger.error('Create module error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to create module'
    })
  }
})

/**
 * @route   PUT /api/modules/:id
 * @desc    Update module
 * @access  Private (requires Modules:update permission)
 */
router.put('/:id', checkPermission('Modules', 'update'), updateModuleValidation, async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      })
    }

    const { id } = req.params
    const { name, description, isActive } = req.body

    // Check if module exists
    const existingModule = await prisma.module.findUnique({
      where: { id }
    })

    if (!existingModule) {
      return res.status(404).json({
        success: false,
        error: 'Module not found'
      })
    }

    // Check for name conflicts
    if (name) {
      const conflictModule = await prisma.module.findFirst({
        where: {
          AND: [{ id: { not: id } }, { name }]
        }
      })

      if (conflictModule) {
        return res.status(400).json({
          success: false,
          error: 'Module with this name already exists'
        })
      }
    }

    // Update module
    const updateData = {}
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (isActive !== undefined) updateData.isActive = isActive

    const module = await prisma.module.update({
      where: { id },
      data: updateData
    })

    logger.info(`Module updated by ${req.user.email}: ${module.name}`)

    res.json({
      success: true,
      message: 'Module updated successfully',
      data: { module }
    })
  } catch (error) {
    logger.error('Update module error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to update module'
    })
  }
})

/**
 * @route   DELETE /api/modules/:id
 * @desc    Delete module
 * @access  Private (requires Modules:delete permission)
 */
router.delete('/:id', checkPermission('Modules', 'delete'), async (req, res) => {
  try {
    const { id } = req.params

    // Check if module exists
    const existingModule = await prisma.module.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            permissions: true
          }
        }
      }
    })

    if (!existingModule) {
      return res.status(404).json({
        success: false,
        error: 'Module not found'
      })
    }

    // Check if it's a system module (prevent deletion of critical modules)
    const systemModules = ['Users', 'Groups', 'Roles', 'Modules', 'Permissions']
    if (systemModules.includes(existingModule.name)) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete system modules'
      })
    }

    // Delete module (cascade will handle related records)
    await prisma.module.delete({
      where: { id }
    })

    logger.info(`Module deleted by ${req.user.email}: ${existingModule.name}`)

    res.json({
      success: true,
      message: 'Module deleted successfully'
    })
  } catch (error) {
    logger.error('Delete module error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to delete module'
    })
  }
})

export default router
