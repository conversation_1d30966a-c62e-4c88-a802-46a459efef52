import express from 'express'
import { body } from 'express-validator'
import { generateToken, hashPassword, comparePassword } from '../utils/auth.js'
import { authLimiter as enhancedAuthLimiter } from '../middleware/security.js'
import { handleValidationErrors } from '../middleware/validation.js'
import { authenticate } from '../middleware/auth.js'
import prisma from '../utils/database.js'
import logger from '../utils/logger.js'

const router = express.Router()

// Validation rules
const registerValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, and underscores'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must be at least 8 characters with uppercase, lowercase, number, and special character'),
  body('firstName').optional().isLength({ min: 1, max: 50 }).trim().withMessage('First name must be 1-50 characters'),
  body('lastName').optional().isLength({ min: 1, max: 50 }).trim().withMessage('Last name must be 1-50 characters')
]

const loginValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required')
]

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', enhancedAuthLimiter, registerValidation, handleValidationErrors, async (req, res) => {
  try {
    const { email, username, password, firstName, lastName } = req.body

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ email }, { username }]
      }
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email or username already exists'
      })
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        firstName: firstName || null,
        lastName: lastName || null
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        createdAt: true
      }
    })

    // Add user to default "Users" group
    const defaultGroup = await prisma.group.findUnique({
      where: { name: 'Users' }
    })

    if (defaultGroup) {
      await prisma.userGroup.create({
        data: {
          userId: user.id,
          groupId: defaultGroup.id
        }
      })
    }

    // Generate token
    const token = generateToken({ userId: user.id })

    logger.info(`New user registered: ${user.email}`)

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user,
        token
      }
    })
  } catch (error) {
    logger.error('Registration error:', error)
    res.status(500).json({
      success: false,
      error: 'Registration failed'
    })
  }
})

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', enhancedAuthLimiter, loginValidation, handleValidationErrors, async (req, res) => {
  try {
    const { email, password } = req.body

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        username: true,
        password: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      })
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        error: 'Account is deactivated'
      })
    }

    // Check password
    const isPasswordValid = await comparePassword(password, user.password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      })
    }

    // Generate token
    const token = generateToken({ userId: user.id })

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user

    logger.info(`User logged in: ${user.email}`)

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userWithoutPassword,
        token
      }
    })
  } catch (error) {
    logger.error('Login error:', error)
    res.status(500).json({
      success: false,
      error: 'Login failed'
    })
  }
})

/**
 * @route   GET /api/auth/me
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/me', authenticate, async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        groupMemberships: {
          include: {
            group: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        }
      }
    })

    res.json({
      success: true,
      data: { user }
    })
  } catch (error) {
    logger.error('Get profile error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile'
    })
  }
})

/**
 * @route   POST /api/auth/oauth-register
 * @desc    Register a new user with OAuth
 * @access  Public
 */
router.post('/oauth-login', async (req, res) => {
  try {
    const { email, provider, providerId } = req.body

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    })

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      })
    }

    // Generate token
    const token = generateToken({ userId: user.id })

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user

    logger.info(`User logged in: ${user.email}`)

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userWithoutPassword,
        token
      }
    })
  } catch (err) {
    logger.error('OAuth login error:', err)
    res.status(500).json({
      success: false,
      error: 'Login failed'
    })
  }
})

router.post('/oauth-register', async (req, res) => {
  try {
    const { email, username, firstName, lastName, provider, providerId } = req.body

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [{ email }, { username }]
      }
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email or username already exists'
      })
    }

    // Create user with a random password since they will authenticate with OAuth
    const randomPassword = await hashPassword(Math.random().toString(36))

    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: randomPassword,
        firstName: firstName || null,
        lastName: lastName || null,
        isActive: true
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true
      }
    })

    // Add user to default "Users" group
    const defaultGroup = await prisma.group.findUnique({
      where: { name: 'Users' }
    })

    if (defaultGroup) {
      await prisma.userGroup.create({
        data: {
          userId: user.id,
          groupId: defaultGroup.id
        }
      })
    }

    // Generate Token
    const token = generateToken({ userId: user.id })

    logger.info(`New user registered: ${user.email}`)

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user,
        token
      }
    })
  } catch (err) {
    logger.error('OAuth register error:', err)
    res.status(500).json({
      success: false,
      error: 'Registration failed'
    })
  }
})

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (client-side token removal)
 * @access  Private
 */
router.post('/logout', authenticate, (req, res) => {
  logger.info(`User logged out: ${req.user.email}`)

  res.json({
    success: true,
    message: 'Logout successful'
  })
})

export default router
