{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:07:18.182Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:07:35.622Z"}
{"level":"info","message":"Server running on port 3002 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:07:53.408Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-03T08:08:07.123Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:08:28.162Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-03T08:34:46.222Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:34:49.510Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:39:18.837Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:39:41.864Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:40:30.462Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:41:12.647Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:41:29.325Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:42:26.144Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T08:42:43.610Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:33:49 +0000] \"GET /api/modules?page=1&limit=10&search=&isActive=true HTTP/1.1\" 401 61 \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:33:49.560Z"}
{"level":"info","message":"Database info: Starting a postgresql pool with 13 connections.","service":"iam-backend","target":"quaint::pooled","timestamp":"2025-08-03T09:34:01.449Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:34:01 +0000] \"POST /api/auth/login HTTP/1.1\" 401 47 \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:34:01.903Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-03T09:34:30.514Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:34:30 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:34:30.516Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:34:42 +0000] \"GET /api/auth/me HTTP/1.1\" 200 536 \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:34:42.772Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:34:53 +0000] \"GET /api/users?page=1&limit=10&search=&isActive=true HTTP/1.1\" 200 594 \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:34:53.693Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:35:11 +0000] \"POST /api/auth/oauth-login HTTP/1.1\" 401 47 \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:35:11.977Z"}
{"level":"info","message":"User <NAME_EMAIL>: <EMAIL>","service":"iam-backend","timestamp":"2025-08-03T09:35:33.979Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:35:33 +0000] \"POST /api/users HTTP/1.1\" 201 247 \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:35:33.980Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:37:17 +0000] \"GET /api/modules?page=1&limit=10&search=&isActive=true HTTP/1.1\" 200 - \"-\" \"PostmanRuntime/7.45.0\"","service":"iam-backend","timestamp":"2025-08-03T09:37:17.647Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T09:39:00.841Z"}
{"level":"info","message":"Database info: Starting a postgresql pool with 13 connections.","service":"iam-backend","target":"quaint::pooled","timestamp":"2025-08-03T09:42:32.098Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-03T09:42:32.512Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:42:32 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"node\"","service":"iam-backend","timestamp":"2025-08-03T09:42:32.518Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-03T09:55:44.069Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:55:44 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"node\"","service":"iam-backend","timestamp":"2025-08-03T09:55:44.070Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-03T09:58:21.248Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:09:58:21 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"node\"","service":"iam-backend","timestamp":"2025-08-03T09:58:21.249Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T14:24:58.605Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T14:26:40.633Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T14:29:14.698Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T14:29:49.545Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T23:06:31.263Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-03T23:06:52.710Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-03T23:10:29.957Z"}
{"level":"info","message":"Database info: Starting a postgresql pool with 13 connections.","service":"iam-backend","target":"quaint::pooled","timestamp":"2025-08-03T23:11:56.046Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-03T23:11:56.900Z"}
{"level":"info","message":"::1 - - [03/Aug/2025:23:11:56 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"node\"","service":"iam-backend","timestamp":"2025-08-03T23:11:56.904Z"}
{"level":"error","message":"Database error: Error in PostgreSQL connection: Error { kind: Closed, cause: None }","service":"iam-backend","target":"quaint::connector::postgres::native","timestamp":"2025-08-03T23:17:08.800Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-04T00:33:15.049Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T00:33:25.944Z"}
{"level":"info","message":"Database info: Starting a postgresql pool with 13 connections.","service":"iam-backend","target":"quaint::pooled","timestamp":"2025-08-04T00:33:45.455Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-04T00:33:47.906Z"}
{"level":"info","message":"::1 - - [04/Aug/2025:00:33:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"node\"","service":"iam-backend","timestamp":"2025-08-04T00:33:47.910Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T00:35:37.099Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T00:36:29.557Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T00:37:06.087Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T02:09:32.076Z"}
{"level":"info","message":"::1 - - [04/Aug/2025:02:13:33 +0000] \"GET / HTTP/1.1\" 404 52 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36\"","service":"iam-backend","timestamp":"2025-08-04T02:13:33.934Z"}
{"level":"info","message":"::1 - - [04/Aug/2025:02:13:34 +0000] \"GET /favicon.ico HTTP/1.1\" 404 63 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36\"","service":"iam-backend","timestamp":"2025-08-04T02:13:34.060Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-04T02:14:01.194Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T02:14:20.458Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T02:14:30.303Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T02:14:37.701Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-04T02:15:29.484Z"}
{"level":"info","message":"Server running on port 3001 in production mode","service":"iam-backend","timestamp":"2025-08-04T02:15:33.466Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-04T02:15:56.215Z"}
{"level":"info","message":"Server running on port 3001 in production mode","service":"iam-backend","timestamp":"2025-08-04T02:52:55.504Z"}
{"level":"info","message":"Server running on port 3001 in production mode","service":"iam-backend","timestamp":"2025-08-04T02:54:39.581Z"}
{"level":"info","message":"::1 - - [04/Aug/2025:02:54:39 +0000] \"GET /health HTTP/1.1\" 200 73 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"iam-backend","timestamp":"2025-08-04T02:54:39.818Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-04T03:16:25.955Z"}
{"level":"info","message":"Server running on port 3001 in production mode","service":"iam-backend","timestamp":"2025-08-04T03:18:50.294Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-04T03:19:04.484Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-04T03:19:06.562Z"}
{"level":"info","message":"Database info: Starting a postgresql pool with 13 connections.","service":"iam-backend","target":"quaint::pooled","timestamp":"2025-08-04T03:21:43.615Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-04T03:21:46.763Z"}
{"level":"info","message":"::1 - - [04/Aug/2025:03:21:46 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"node\"","service":"iam-backend","timestamp":"2025-08-04T03:21:46.767Z"}
{"level":"error","message":"Database error: Error in PostgreSQL connection: Error { kind: Closed, cause: None }","service":"iam-backend","target":"quaint::connector::postgres::native","timestamp":"2025-08-04T03:29:38.890Z"}
{"level":"info","message":"User logged in: <EMAIL>","service":"iam-backend","timestamp":"2025-08-04T04:41:49.301Z"}
{"level":"info","message":"::1 - - [04/Aug/2025:04:41:49 +0000] \"POST /api/auth/login HTTP/1.1\" 200 395 \"-\" \"node\"","service":"iam-backend","timestamp":"2025-08-04T04:41:49.303Z"}
{"level":"error","message":"Database error: Error in PostgreSQL connection: Error { kind: Closed, cause: None }","service":"iam-backend","target":"quaint::connector::postgres::native","timestamp":"2025-08-04T04:47:17.226Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"iam-backend","timestamp":"2025-08-04T09:53:08.178Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-10T11:50:43.034Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-10T11:58:44.543Z"}
{"level":"info","message":"Server running on port 3001 in development mode","service":"iam-backend","timestamp":"2025-08-10T11:58:56.264Z"}
