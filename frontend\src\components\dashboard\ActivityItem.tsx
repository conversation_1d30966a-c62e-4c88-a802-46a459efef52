import { ReactNode } from 'react'
import { MapPinIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline'

interface ActivityItemProps {
  title: string
  location: string
  price: string
  time: string
  icon: ReactNode
  iconColor?: string
}

export default function ActivityItem({
  title,
  location,
  price,
  time,
  icon,
  iconColor = 'text-gray-500'
}: ActivityItemProps) {
  return (
    <div
      className="p-4 sm:p-6 hover:bg-gray-50 transition-colors"
      role="article"
      aria-label={`Property activity: ${title}`}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0" aria-hidden="true">
          <div className={iconColor}>{icon}</div>
        </div>
        <div className="ml-3 sm:ml-4 flex-1 min-w-0">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-0">
            <h3 className="text-sm font-medium text-gray-900 truncate pr-2">{title}</h3>
            <time className="text-xs sm:text-sm text-gray-500 flex-shrink-0">{time}</time>
          </div>
          <div className="mt-1 flex flex-col sm:flex-row sm:items-center text-xs sm:text-sm text-gray-500 gap-1 sm:gap-0">
            <div className="flex items-center">
              <MapPinIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" aria-hidden="true" />
              <span className="truncate" aria-label={`Location: ${location}`}>
                {location}
              </span>
            </div>
            <div className="flex items-center sm:ml-4">
              <CurrencyDollarIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" aria-hidden="true" />
              <span className="font-medium text-gray-900" aria-label={`Price: ${price}`}>
                {price}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
