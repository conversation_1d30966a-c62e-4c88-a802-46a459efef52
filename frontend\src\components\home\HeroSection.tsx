'use client'

import { PropertySearchForm, SearchFilters } from '@/components/search'

const HeroSection = () => {
  const handleSearch = (filters: SearchFilters) => {
    console.log('Search filters:', filters)
    // TODO: Implement search functionality - redirect to properties page with filters
    // For now, we'll just log the filters
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"%3E%3Cdefs%3E%3ClinearGradient id="bg" x1="0%25" y1="0%25" x2="100%25" y2="100%25"%3E%3Cstop offset="0%25" style="stop-color:%23667eea;stop-opacity:1" /%3E%3Cstop offset="100%25" style="stop-color:%23764ba2;stop-opacity:1" /%3E%3C/linearGradient%3E%3C/defs%3E%3Crect width="1920" height="1080" fill="url(%23bg)"/%3E%3Cg opacity="0.1"%3E%3Cpath d="M0,0 L1920,0 L1920,540 L960,1080 L0,540 Z" fill="%23ffffff"/%3E%3C/g%3E%3C/svg%3E')`
        }}
      ></div>

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/70 via-gray-800/60 to-lime-900/70"></div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.4%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Hero Text */}
        <div className="mb-12">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Find Your
            <span className="block text-lime-400">Dream Property</span>
          </h1>
          <p className="text-xl sm:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed">
            Discover premium real estate opportunities in the Philippines&apos; most desirable locations
          </p>
        </div>

        {/* Search Form */}
        <div className="max-w-4xl mx-auto">
          <PropertySearchForm onSearch={handleSearch} layout="horizontal" showAdvanced={false} />

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 text-white">
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-lime-400 mb-2">1000+</div>
              <div className="text-sm sm:text-base text-gray-300">Properties Listed</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-lime-400 mb-2">500+</div>
              <div className="text-sm sm:text-base text-gray-300">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-lime-400 mb-2">50+</div>
              <div className="text-sm sm:text-base text-gray-300">Cities Covered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-lime-400 mb-2">15+</div>
              <div className="text-sm sm:text-base text-gray-300">Years Experience</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </section>
  )
}

export default HeroSection
