import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Starting database seeding...')

  // Create modules
  const modules = await Promise.all([
    prisma.module.upsert({
      where: { name: 'Users' },
      update: {},
      create: {
        name: 'Users',
        description: 'User management module'
      }
    }),
    prisma.module.upsert({
      where: { name: 'Groups' },
      update: {},
      create: {
        name: 'Groups',
        description: 'Group management module'
      }
    }),
    prisma.module.upsert({
      where: { name: 'Roles' },
      update: {},
      create: {
        name: 'Roles',
        description: 'Role management module'
      }
    }),
    prisma.module.upsert({
      where: { name: 'Modules' },
      update: {},
      create: {
        name: 'Modules',
        description: 'Module management module'
      }
    }),
    prisma.module.upsert({
      where: { name: 'Permissions' },
      update: {},
      create: {
        name: 'Permissions',
        description: 'Permission management module'
      }
    })
  ])

  console.log(
    'Created modules:',
    modules.map(m => m.name)
  )

  // Create permissions for each module
  const actions = ['create', 'read', 'update', 'delete']
  const permissions = []

  for (const module of modules) {
    for (const action of actions) {
      const permission = await prisma.permission.upsert({
        where: {
          moduleId_action: {
            moduleId: module.id,
            action: action
          }
        },
        update: {},
        create: {
          action: action,
          description: `${action.charAt(0).toUpperCase() + action.slice(1)} ${module.name.toLowerCase()}`,
          moduleId: module.id
        }
      })
      permissions.push(permission)
    }
  }

  console.log('Created permissions:', permissions.length)

  // Create roles
  const adminRole = await prisma.role.upsert({
    where: { name: 'Admin' },
    update: {},
    create: {
      name: 'Admin',
      description: 'Full system administrator'
    }
  })

  const userRole = await prisma.role.upsert({
    where: { name: 'User' },
    update: {},
    create: {
      name: 'User',
      description: 'Basic user role'
    }
  })

  console.log('Created roles:', [adminRole.name, userRole.name])

  // Assign all permissions to admin role
  for (const permission of permissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id
        }
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id
      }
    })
  }

  // Assign read permissions to user role
  const readPermissions = permissions.filter(p => p.action === 'read')
  for (const permission of readPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: userRole.id,
          permissionId: permission.id
        }
      },
      update: {},
      create: {
        roleId: userRole.id,
        permissionId: permission.id
      }
    })
  }

  // Create groups
  const adminGroup = await prisma.group.upsert({
    where: { name: 'Administrators' },
    update: {},
    create: {
      name: 'Administrators',
      description: 'System administrators group'
    }
  })

  const usersGroup = await prisma.group.upsert({
    where: { name: 'Users' },
    update: {},
    create: {
      name: 'Users',
      description: 'Regular users group'
    }
  })

  console.log('Created groups:', [adminGroup.name, usersGroup.name])

  // Assign roles to groups
  await prisma.groupRole.upsert({
    where: {
      groupId_roleId: {
        groupId: adminGroup.id,
        roleId: adminRole.id
      }
    },
    update: {},
    create: {
      groupId: adminGroup.id,
      roleId: adminRole.id
    }
  })

  await prisma.groupRole.upsert({
    where: {
      groupId_roleId: {
        groupId: usersGroup.id,
        roleId: userRole.id
      }
    },
    update: {},
    create: {
      groupId: usersGroup.id,
      roleId: userRole.id
    }
  })

  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator'
    }
  })

  // Add admin user to admin group
  await prisma.userGroup.upsert({
    where: {
      userId_groupId: {
        userId: adminUser.id,
        groupId: adminGroup.id
      }
    },
    update: {},
    create: {
      userId: adminUser.id,
      groupId: adminGroup.id
    }
  })

  console.log('Created admin user:', adminUser.email)
  console.log('Database seeding completed successfully!')
}

main()
  .catch(e => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
