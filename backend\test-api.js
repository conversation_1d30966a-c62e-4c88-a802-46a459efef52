/**
 * Basic API Testing Script
 * 
 * This script provides basic testing for the IAM backend API endpoints.
 * Run with: node test-api.js
 * 
 * Make sure the server is running on http://localhost:3001
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';
let authToken = '';
let testUserId = '';
let testGroupId = '';
let testRoleId = '';
let testModuleId = '';
let testPermissionId = '';

// Test configuration
const testUser = {
  email: '<EMAIL>',
  username: 'testuser',
  password: 'TestPass123!',
  firstName: 'Test',
  lastName: 'User'
};

const adminUser = {
  email: '<EMAIL>',
  password: 'Admin123!'
};

/**
 * Helper function to make API requests
 */
async function apiRequest(method, endpoint, data = null, useAuth = true) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
}

/**
 * Test runner function
 */
async function runTest(testName, testFunction) {
  console.log(`\n🧪 Testing: ${testName}`);
  try {
    await testFunction();
    console.log(`✅ ${testName} - PASSED`);
  } catch (error) {
    console.log(`❌ ${testName} - FAILED: ${error.message}`);
  }
}

/**
 * Test health check endpoint
 */
async function testHealthCheck() {
  const result = await apiRequest('GET', '/health', null, false);
  if (!result.success || result.status !== 200) {
    throw new Error(`Health check failed: ${JSON.stringify(result.error)}`);
  }
}

/**
 * Test user registration
 */
async function testUserRegistration() {
  const result = await apiRequest('POST', '/auth/register', testUser, false);
  if (!result.success || result.status !== 201) {
    throw new Error(`Registration failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!result.data.data.token) {
    throw new Error('No token returned from registration');
  }
}

/**
 * Test admin login
 */
async function testAdminLogin() {
  const result = await apiRequest('POST', '/auth/login', adminUser, false);
  if (!result.success || result.status !== 200) {
    throw new Error(`Admin login failed: ${JSON.stringify(result.error)}`);
  }
  
  authToken = result.data.data.token;
  if (!authToken) {
    throw new Error('No token returned from login');
  }
}

/**
 * Test getting user profile
 */
async function testGetProfile() {
  const result = await apiRequest('GET', '/auth/profile');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get profile failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!result.data.data.user.email) {
    throw new Error('Profile data incomplete');
  }
}

/**
 * Test getting all users
 */
async function testGetUsers() {
  const result = await apiRequest('GET', '/users');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get users failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!Array.isArray(result.data.data.users)) {
    throw new Error('Users data is not an array');
  }
  
  // Find the test user we created
  const testUserFound = result.data.data.users.find(u => u.email === testUser.email);
  if (testUserFound) {
    testUserId = testUserFound.id;
  }
}

/**
 * Test creating a new user
 */
async function testCreateUser() {
  const newUser = {
    email: '<EMAIL>',
    username: 'newtest',
    password: 'NewTest123!',
    firstName: 'New',
    lastName: 'Test'
  };
  
  const result = await apiRequest('POST', '/users', newUser);
  if (!result.success || result.status !== 201) {
    throw new Error(`Create user failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!result.data.data.user.id) {
    throw new Error('No user ID returned from creation');
  }
}

/**
 * Test getting all groups
 */
async function testGetGroups() {
  const result = await apiRequest('GET', '/groups');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get groups failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!Array.isArray(result.data.data.groups)) {
    throw new Error('Groups data is not an array');
  }
  
  // Get the first group ID for testing
  if (result.data.data.groups.length > 0) {
    testGroupId = result.data.data.groups[0].id;
  }
}

/**
 * Test creating a new group
 */
async function testCreateGroup() {
  const newGroup = {
    name: 'Test Group',
    description: 'A test group for API testing',
    isActive: true
  };
  
  const result = await apiRequest('POST', '/groups', newGroup);
  if (!result.success || result.status !== 201) {
    throw new Error(`Create group failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!result.data.data.group.id) {
    throw new Error('No group ID returned from creation');
  }
}

/**
 * Test getting all roles
 */
async function testGetRoles() {
  const result = await apiRequest('GET', '/roles');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get roles failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!Array.isArray(result.data.data.roles)) {
    throw new Error('Roles data is not an array');
  }
  
  // Get the first role ID for testing
  if (result.data.data.roles.length > 0) {
    testRoleId = result.data.data.roles[0].id;
  }
}

/**
 * Test creating a new role
 */
async function testCreateRole() {
  const newRole = {
    name: 'Test Role',
    description: 'A test role for API testing',
    isActive: true
  };
  
  const result = await apiRequest('POST', '/roles', newRole);
  if (!result.success || result.status !== 201) {
    throw new Error(`Create role failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!result.data.data.role.id) {
    throw new Error('No role ID returned from creation');
  }
}

/**
 * Test getting all modules
 */
async function testGetModules() {
  const result = await apiRequest('GET', '/modules');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get modules failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!Array.isArray(result.data.data.modules)) {
    throw new Error('Modules data is not an array');
  }
  
  // Get the first module ID for testing
  if (result.data.data.modules.length > 0) {
    testModuleId = result.data.data.modules[0].id;
  }
}

/**
 * Test creating a new module
 */
async function testCreateModule() {
  const newModule = {
    name: 'Test Module',
    description: 'A test module for API testing',
    isActive: true
  };
  
  const result = await apiRequest('POST', '/modules', newModule);
  if (!result.success || result.status !== 201) {
    throw new Error(`Create module failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!result.data.data.module.id) {
    throw new Error('No module ID returned from creation');
  }
}

/**
 * Test getting all permissions
 */
async function testGetPermissions() {
  const result = await apiRequest('GET', '/permissions');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get permissions failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!Array.isArray(result.data.data.permissions)) {
    throw new Error('Permissions data is not an array');
  }
  
  // Get the first permission ID for testing
  if (result.data.data.permissions.length > 0) {
    testPermissionId = result.data.data.permissions[0].id;
  }
}

/**
 * Test getting user permissions
 */
async function testGetUserPermissions() {
  const result = await apiRequest('GET', '/me/permissions');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get user permissions failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!result.data.data.permissions) {
    throw new Error('No permissions data returned');
  }
}

/**
 * Test permission simulation
 */
async function testSimulateAction() {
  if (!testUserId) {
    console.log('⚠️  Skipping simulate action test - no test user ID available');
    return;
  }
  
  const simulation = {
    userId: testUserId,
    module: 'Users',
    action: 'read'
  };
  
  const result = await apiRequest('POST', '/simulate-action', simulation);
  if (!result.success || result.status !== 200) {
    throw new Error(`Simulate action failed: ${JSON.stringify(result.error)}`);
  }
  
  if (typeof result.data.data.simulation.hasAccess !== 'boolean') {
    throw new Error('Simulation result incomplete');
  }
}

/**
 * Test permission check
 */
async function testPermissionCheck() {
  const result = await apiRequest('GET', '/permissions/check?module=Users&action=read');
  if (!result.success || result.status !== 200) {
    throw new Error(`Permission check failed: ${JSON.stringify(result.error)}`);
  }
  
  if (typeof result.data.data.hasAccess !== 'boolean') {
    throw new Error('Permission check result incomplete');
  }
}

/**
 * Test getting available modules
 */
async function testGetAvailableModules() {
  const result = await apiRequest('GET', '/modules/available');
  if (!result.success || result.status !== 200) {
    throw new Error(`Get available modules failed: ${JSON.stringify(result.error)}`);
  }
  
  if (!Array.isArray(result.data.data.modules)) {
    throw new Error('Available modules data is not an array');
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🚀 Starting IAM Backend API Tests');
  console.log('=====================================');
  
  // Health and Authentication Tests
  await runTest('Health Check', testHealthCheck);
  await runTest('User Registration', testUserRegistration);
  await runTest('Admin Login', testAdminLogin);
  await runTest('Get Profile', testGetProfile);
  
  // User Management Tests
  await runTest('Get Users', testGetUsers);
  await runTest('Create User', testCreateUser);
  
  // Group Management Tests
  await runTest('Get Groups', testGetGroups);
  await runTest('Create Group', testCreateGroup);
  
  // Role Management Tests
  await runTest('Get Roles', testGetRoles);
  await runTest('Create Role', testCreateRole);
  
  // Module Management Tests
  await runTest('Get Modules', testGetModules);
  await runTest('Create Module', testCreateModule);
  
  // Permission Management Tests
  await runTest('Get Permissions', testGetPermissions);
  
  // Access Control Tests
  await runTest('Get User Permissions', testGetUserPermissions);
  await runTest('Simulate Action', testSimulateAction);
  await runTest('Permission Check', testPermissionCheck);
  await runTest('Get Available Modules', testGetAvailableModules);
  
  console.log('\n🏁 Test Suite Complete');
  console.log('=====================================');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  apiRequest,
  BASE_URL
};
