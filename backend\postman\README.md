# IAM Backend API - Postman Collections

This directory contains comprehensive Postman collections for testing the IAM Backend API endpoints.

## Files

- **`IAM-Backend-API.postman_collection.json`** - Main collection with Authentication, Users, Groups, and Roles endpoints
- **`IAM-Backend-API-Extended.postman_collection.json`** - Extended collection with Modules, Permissions, and Access Control endpoints
- **`Local-Environment.postman_environment.json`** - Environment variables for local development
- **`README.md`** - This documentation file

## Setup Instructions

### 1. Import Collections and Environment

1. Open Postman
2. Click **Import** button
3. Import all three JSON files:
   - `IAM-Backend-API.postman_collection.json`
   - `IAM-Backend-API-Extended.postman_collection.json`
   - `Local-Environment.postman_environment.json`

### 2. Select Environment

1. In Postman, select **"Local IAM Backend Environment"** from the environment dropdown (top right)
2. Verify the `baseUrl` is set to `http://localhost:3000` (or your server port)

### 3. Start the Backend Server

Make sure your backend server is running:

```bash
cd backend
npm start
# or
npm run dev
```

## Usage Guide

### Authentication Flow

1. **Health Check** - Test server connectivity
2. **Register User** - Create a new user account (optional)
3. **Login User** - Authenticate and get JWT token
   - The login request automatically saves the JWT token to the `authToken` environment variable
   - The user ID is saved to the `userId` environment variable

### Testing Workflow

#### Basic CRUD Operations
1. Start with **Authentication** → **Login User** to get your JWT token
2. Test **Users** endpoints to manage user accounts
3. Test **Groups** endpoints to manage user groups
4. Test **Roles** endpoints to manage roles

#### Advanced IAM Features (Extended Collection)
1. Test **Modules** endpoints to manage system modules
2. Test **Permissions** endpoints to manage permissions
3. Test **Access Control** endpoints to check user permissions

### Environment Variables

The following variables are automatically populated during testing:

- `authToken` - JWT token from login
- `userId` - User ID from login/user creation
- `groupId` - Group ID from group creation
- `roleId` - Role ID from role creation
- `moduleId` - Module ID from module creation
- `permissionId` - Permission ID from permission creation

### Manual Configuration

You can manually set these environment variables if needed:

- `adminEmail` - Admin user email (default: <EMAIL>)
- `adminPassword` - Admin user password (default: AdminPass123!)
- `testEmail` - Test user email (default: <EMAIL>)
- `testUsername` - Test username (default: testuser)
- `testPassword` - Test user password (default: TestPass123!)

## API Endpoints Overview

### Main Collection (IAM-Backend-API)

#### Health Check
- `GET /health` - Server health check

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user profile
- `POST /api/auth/oauth-login` - OAuth login
- `POST /api/auth/oauth-register` - OAuth registration
- `POST /api/auth/logout` - User logout

#### Users
- `GET /api/users` - Get all users (with pagination)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create new user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user
- `PUT /api/users/:id/password` - Change user password
- `POST /api/users/by-email` - Get user by email

#### Groups
- `GET /api/groups` - Get all groups
- `GET /api/groups/:id` - Get group by ID
- `POST /api/groups` - Create new group
- `PUT /api/groups/:id` - Update group
- `DELETE /api/groups/:id` - Delete group
- `POST /api/groups/:groupId/users` - Assign users to group
- `DELETE /api/groups/:groupId/users/:userId` - Remove user from group

#### Roles
- `GET /api/roles` - Get all roles
- `GET /api/roles/:id` - Get role by ID
- `POST /api/roles` - Create new role
- `PUT /api/roles/:id` - Update role
- `DELETE /api/roles/:id` - Delete role
- `POST /api/roles/groups/:groupId/roles` - Assign roles to group
- `DELETE /api/roles/groups/:groupId/roles/:roleId` - Remove role from group

### Extended Collection (IAM-Backend-API-Extended)

#### Modules
- `GET /api/modules` - Get all modules
- `GET /api/modules/:id` - Get module by ID
- `POST /api/modules` - Create new module
- `PUT /api/modules/:id` - Update module
- `DELETE /api/modules/:id` - Delete module

#### Permissions
- `GET /api/permissions` - Get all permissions
- `GET /api/permissions/:id` - Get permission by ID
- `POST /api/permissions` - Create new permission
- `PUT /api/permissions/:id` - Update permission
- `DELETE /api/permissions/:id` - Delete permission
- `POST /api/permissions/roles/:roleId/permissions` - Assign permissions to role
- `DELETE /api/permissions/roles/:roleId/permissions/:permissionId` - Remove permission from role

#### Access Control
- `GET /api/me/permissions` - Get current user's permissions
- `POST /api/simulate-action` - Test user's ability to perform action
- `GET /api/permissions/check` - Check if current user has specific permission
- `GET /api/modules/available` - Get all available modules and permissions

## Notes

- All endpoints (except health check, register, login, and oauth endpoints) require authentication
- JWT tokens are automatically included in requests when using the environment variables
- Response data is automatically parsed to populate environment variables where applicable
- Use the test scripts in the requests to automatically capture IDs for subsequent requests

## Troubleshooting

1. **401 Unauthorized** - Make sure you're logged in and the `authToken` is set
2. **404 Not Found** - Verify the server is running and the `baseUrl` is correct
3. **500 Internal Server Error** - Check server logs for detailed error information
4. **Missing Environment Variables** - Ensure you've selected the correct environment and run the login request

For more information about the API, refer to the backend source code and documentation.
