{"info": {"_postman_id": "iam-backend-api-extended-collection", "name": "IAM Backend API - Extended", "description": "Extended API collection for Modules, Permissions, and Access Control endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Get All Modules", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/modules?page=1&limit=10&search=&isActive=true", "host": ["{{baseUrl}}"], "path": ["api", "modules"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "isActive", "value": "true"}]}}}, {"name": "Get Module by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/modules/{{moduleId}}", "host": ["{{baseUrl}}"], "path": ["api", "modules", "{{moduleId}}"]}}}, {"name": "Create Mo<PERSON>le", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data.module.id) {", "        pm.environment.set('moduleId', response.data.module.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Module\",\n  \"description\": \"A test module for API testing\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/modules", "host": ["{{baseUrl}}"], "path": ["api", "modules"]}}}, {"name": "Update Module", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Test Module\",\n  \"description\": \"Updated description\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/modules/{{moduleId}}", "host": ["{{baseUrl}}"], "path": ["api", "modules", "{{moduleId}}"]}}}, {"name": "Delete Module", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/modules/{{moduleId}}", "host": ["{{baseUrl}}"], "path": ["api", "modules", "{{moduleId}}"]}}}]}, {"name": "Permissions", "item": [{"name": "Get All Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/permissions?page=1&limit=10&search=&isActive=true&moduleId=", "host": ["{{baseUrl}}"], "path": ["api", "permissions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": ""}, {"key": "isActive", "value": "true"}, {"key": "moduleId", "value": ""}]}}}, {"name": "Get Permission by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/permissions/{{permissionId}}", "host": ["{{baseUrl}}"], "path": ["api", "permissions", "{{permissionId}}"]}}}, {"name": "Create Permission", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data.permission.id) {", "        pm.environment.set('permissionId', response.data.permission.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"moduleId\": \"{{moduleId}}\",\n  \"action\": \"test\",\n  \"description\": \"Test permission for API testing\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/permissions", "host": ["{{baseUrl}}"], "path": ["api", "permissions"]}}}, {"name": "Update Permission", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"updated-test\",\n  \"description\": \"Updated test permission\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/permissions/{{permissionId}}", "host": ["{{baseUrl}}"], "path": ["api", "permissions", "{{permissionId}}"]}}}, {"name": "Delete Permission", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/permissions/{{permissionId}}", "host": ["{{baseUrl}}"], "path": ["api", "permissions", "{{permissionId}}"]}}}, {"name": "Assign Permissions to Role", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"permissionIds\": [\"{{permissionId}}\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/permissions/roles/{{roleId}}/permissions", "host": ["{{baseUrl}}"], "path": ["api", "permissions", "roles", "{{roleId}}", "permissions"]}}}, {"name": "Remove Permission from Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/permissions/roles/{{roleId}}/permissions/{{permissionId}}", "host": ["{{baseUrl}}"], "path": ["api", "permissions", "roles", "{{roleId}}", "permissions", "{{permissionId}}"]}}}]}, {"name": "Access Control", "item": [{"name": "Get My Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/me/permissions", "host": ["{{baseUrl}}"], "path": ["api", "me", "permissions"]}}}, {"name": "Simulate Action", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{userId}}\",\n  \"module\": \"Users\",\n  \"action\": \"read\"\n}"}, "url": {"raw": "{{baseUrl}}/api/simulate-action", "host": ["{{baseUrl}}"], "path": ["api", "simulate-action"]}}}, {"name": "Check Permission", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/permissions/check?module=Users&action=read", "host": ["{{baseUrl}}"], "path": ["api", "permissions", "check"], "query": [{"key": "module", "value": "Users"}, {"key": "action", "value": "read"}]}}}, {"name": "Get Available Modules", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/modules/available", "host": ["{{baseUrl}}"], "path": ["api", "modules", "available"]}}}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}}