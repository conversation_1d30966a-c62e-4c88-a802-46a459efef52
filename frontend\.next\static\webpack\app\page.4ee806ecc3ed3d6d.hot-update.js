"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/navigation/Header.tsx":
/*!**********************************************!*\
  !*** ./src/components/navigation/Header.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Header = ()=>{\n    var _session_user_name, _session_user, _session_user_email, _session_user1, _session_user_name1, _session_user2, _session_user_email1, _session_user3, _session_user4;\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isAuthDropdownOpen, setIsAuthDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const authDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const navigationItems = [\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Developments',\n            href: '/developments'\n        },\n        {\n            name: 'Explore Places',\n            href: '/explore'\n        },\n        {\n            name: 'Properties',\n            href: '/properties'\n        },\n        {\n            name: 'Services',\n            href: '/services'\n        },\n        {\n            name: 'Blog',\n            href: '/blog'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    // Handle click outside to close dropdowns\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Header.useEffect.handleClickOutside\": (event)=>{\n                    if (authDropdownRef.current && !authDropdownRef.current.contains(event.target)) {\n                        setIsAuthDropdownOpen(false);\n                    }\n                    if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                        setIsUserMenuOpen(false);\n                    }\n                }\n            }[\"Header.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/images/logo.png\",\n                                    alt: \"MRH Platform Logo\",\n                                    width: 120,\n                                    height: 40,\n                                    className: \"h-8 lg:h-10 w-auto object-contain\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                status === 'loading' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined) : session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: userMenuRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                            className: \"flex items-center space-x-2 text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_name = _session_user.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.charAt(0)) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_email = _session_user1.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.charAt(0)) || 'U'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/dashboard\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsUserMenuOpen(false),\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/profile\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsUserMenuOpen(false),\n                                                    children: \"Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2 border-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)();\n                                                        setIsUserMenuOpen(false);\n                                                    },\n                                                    className: \"block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: authDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsAuthDropdownOpen(!isAuthDropdownOpen),\n                                            className: \"flex items-center justify-center w-10 h-10 text-gray-700 hover:text-lime-600 hover:bg-gray-50 rounded-full transition-colors duration-200\",\n                                            \"aria-label\": \"User account menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isAuthDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/signin\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsAuthDropdownOpen(false),\n                                                    children: \"Sign in\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/signup\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsAuthDropdownOpen(false),\n                                                    children: \"Create new account\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"bg-lime-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200\",\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-gray-100 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"block w-full bg-lime-500 text-white text-center px-6 py-3 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200 mb-4\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined),\n                                session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: ((_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : (_session_user_name1 = _session_user2.name) === null || _session_user_name1 === void 0 ? void 0 : _session_user_name1.charAt(0)) || ((_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : (_session_user_email1 = _session_user3.email) === null || _session_user_email1 === void 0 ? void 0 : _session_user_email1.charAt(0)) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: ((_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : _session_user4.name) || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/profile\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signin\",\n                                            className: \"block w-full text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200 text-left\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signup\",\n                                            className: \"block w-full bg-gray-900 text-white text-center px-4 py-2 rounded-lg font-medium hover:bg-gray-800 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Create new account\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"L7KWsbXvK3w4Wt4+DuA7UBkkSQ4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession\n    ];\n});\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/Header.tsx\n"));

/***/ })

});