"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/navigation/Header.tsx":
/*!**********************************************!*\
  !*** ./src/components/navigation/Header.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Header = ()=>{\n    var _session_user_name, _session_user, _session_user_email, _session_user1, _session_user2, _session_user_name1, _session_user3, _session_user_email1, _session_user4, _session_user5;\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isAuthDropdownOpen, setIsAuthDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const authDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const navigationItems = [\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Developments',\n            href: '/developments'\n        },\n        {\n            name: 'Explore Places',\n            href: '/explore'\n        },\n        {\n            name: 'Properties',\n            href: '/properties'\n        },\n        {\n            name: 'Services',\n            href: '/services'\n        },\n        {\n            name: 'Blog',\n            href: '/blog'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    // Handle click outside to close dropdowns\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Header.useEffect.handleClickOutside\": (event)=>{\n                    if (authDropdownRef.current && !authDropdownRef.current.contains(event.target)) {\n                        setIsAuthDropdownOpen(false);\n                    }\n                    if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                        setIsUserMenuOpen(false);\n                    }\n                }\n            }[\"Header.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/images/logo.png\",\n                                    alt: \"MRH Platform Logo\",\n                                    width: 120,\n                                    height: 40,\n                                    className: \"h-8 lg:h-10 w-auto object-contain\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"bg-lime-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200\",\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                status === 'loading' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined) : session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: userMenuRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                            className: \"flex items-center space-x-2 text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_name = _session_user.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.charAt(0)) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_email = _session_user1.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.charAt(0)) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: ((_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.name) || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/dashboard\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsUserMenuOpen(false),\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/profile\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsUserMenuOpen(false),\n                                                    children: \"Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2 border-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)();\n                                                        setIsUserMenuOpen(false);\n                                                    },\n                                                    className: \"block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: authDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsAuthDropdownOpen(!isAuthDropdownOpen),\n                                            className: \"flex items-center justify-center w-10 h-10 text-gray-700 hover:text-lime-600 hover:bg-gray-50 rounded-full transition-colors duration-200\",\n                                            \"aria-label\": \"User account menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isAuthDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/signin\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsAuthDropdownOpen(false),\n                                                    children: \"Sign in\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/signup\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsAuthDropdownOpen(false),\n                                                    children: \"Create new account\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-gray-100 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"block w-full bg-lime-500 text-white text-center px-6 py-3 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200 mb-4\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: ((_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : (_session_user_name1 = _session_user3.name) === null || _session_user_name1 === void 0 ? void 0 : _session_user_name1.charAt(0)) || ((_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : (_session_user_email1 = _session_user4.email) === null || _session_user_email1 === void 0 ? void 0 : _session_user_email1.charAt(0)) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: ((_session_user5 = session.user) === null || _session_user5 === void 0 ? void 0 : _session_user5.name) || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/profile\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block w-full text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200 text-left\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block w-full bg-gray-900 text-white text-center px-4 py-2 rounded-lg font-medium hover:bg-gray-800 transition-colors duration-200\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"L7KWsbXvK3w4Wt4+DuA7UBkkSQ4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession\n    ];\n});\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL25hdmlnYXRpb24vSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUU0QjtBQUNFO0FBQ3FCO0FBQ1U7QUFFN0QsTUFBTVEsU0FBUztRQXNGTUMsb0JBQUFBLGVBQWlDQSxxQkFBQUEsZ0JBRUxBLGdCQXFIMUJBLHFCQUFBQSxnQkFBaUNBLHNCQUFBQSxnQkFFU0E7O0lBOU0vRCxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR1QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDVSxnQkFBZ0JDLGtCQUFrQixHQUFHWCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNZLG9CQUFvQkMsc0JBQXNCLEdBQUdiLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sRUFBRWMsTUFBTVAsT0FBTyxFQUFFUSxNQUFNLEVBQUUsR0FBR1osMkRBQVVBO0lBQzVDLE1BQU1hLGtCQUFrQmYsNkNBQU1BLENBQWlCO0lBQy9DLE1BQU1nQixjQUFjaEIsNkNBQU1BLENBQWlCO0lBRTNDLE1BQU1pQixrQkFBa0I7UUFDdEI7WUFBRUMsTUFBTTtZQUFZQyxNQUFNO1FBQVM7UUFDbkM7WUFBRUQsTUFBTTtZQUFnQkMsTUFBTTtRQUFnQjtRQUM5QztZQUFFRCxNQUFNO1lBQWtCQyxNQUFNO1FBQVc7UUFDM0M7WUFBRUQsTUFBTTtZQUFjQyxNQUFNO1FBQWM7UUFDMUM7WUFBRUQsTUFBTTtZQUFZQyxNQUFNO1FBQVk7UUFDdEM7WUFBRUQsTUFBTTtZQUFRQyxNQUFNO1FBQVE7UUFDOUI7WUFBRUQsTUFBTTtZQUFXQyxNQUFNO1FBQVc7S0FDckM7SUFFRCwwQ0FBMEM7SUFDMUNsQixnREFBU0E7NEJBQUM7WUFDUixNQUFNbUI7dURBQXFCLENBQUNDO29CQUMxQixJQUFJTixnQkFBZ0JPLE9BQU8sSUFBSSxDQUFDUCxnQkFBZ0JPLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixNQUFNRyxNQUFNLEdBQVc7d0JBQ3RGWixzQkFBc0I7b0JBQ3hCO29CQUNBLElBQUlJLFlBQVlNLE9BQU8sSUFBSSxDQUFDTixZQUFZTSxPQUFPLENBQUNDLFFBQVEsQ0FBQ0YsTUFBTUcsTUFBTSxHQUFXO3dCQUM5RWQsa0JBQWtCO29CQUNwQjtnQkFDRjs7WUFFQWUsU0FBU0MsZ0JBQWdCLENBQUMsYUFBYU47WUFDdkM7b0NBQU87b0JBQ0xLLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO2dCQUM1Qzs7UUFDRjsyQkFBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNRO1FBQU9DLFdBQVU7a0JBQ2hCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FDYiw0RUFBQ2hDLGtEQUFJQTtnQ0FBQ3NCLE1BQUs7Z0NBQUlVLFdBQVU7MENBQ3ZCLDRFQUFDL0Isa0RBQUtBO29DQUNKaUMsS0FBSTtvQ0FDSkMsS0FBSTtvQ0FDSkMsT0FBTztvQ0FDUEMsUUFBUTtvQ0FDUkwsV0FBVTtvQ0FDVk0sUUFBUTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNZCw4REFBQ0M7NEJBQUlQLFdBQVU7c0NBQ1paLGdCQUFnQm9CLEdBQUcsQ0FBQ0MsQ0FBQUEscUJBQ25CLDhEQUFDekMsa0RBQUlBO29DQUVIc0IsTUFBTW1CLEtBQUtuQixJQUFJO29DQUNmVSxXQUFVOzhDQUVUUyxLQUFLcEIsSUFBSTttQ0FKTG9CLEtBQUtwQixJQUFJOzs7Ozs7Ozs7O3NDQVVwQiw4REFBQ1k7NEJBQUlELFdBQVU7OzhDQUViLDhEQUFDaEMsa0RBQUlBO29DQUNIc0IsTUFBSztvQ0FDTFUsV0FBVTs4Q0FDWDs7Ozs7O2dDQUtBZixXQUFXLDBCQUNWLDhEQUFDZ0I7b0NBQUlELFdBQVU7Ozs7O2dEQUNidkIsd0JBQ0YsOERBQUN3QjtvQ0FBSUQsV0FBVTtvQ0FBV1UsS0FBS3ZCOztzREFDN0IsOERBQUN3Qjs0Q0FDQ0MsU0FBUyxJQUFNL0Isa0JBQWtCLENBQUNEOzRDQUNsQ29CLFdBQVU7OzhEQUVWLDhEQUFDQztvREFBSUQsV0FBVTs4REFDWnZCLEVBQUFBLGdCQUFBQSxRQUFRb0MsSUFBSSxjQUFacEMscUNBQUFBLHFCQUFBQSxjQUFjWSxJQUFJLGNBQWxCWix5Q0FBQUEsbUJBQW9CcUMsTUFBTSxDQUFDLFNBQU1yQyxpQkFBQUEsUUFBUW9DLElBQUksY0FBWnBDLHNDQUFBQSxzQkFBQUEsZUFBY3NDLEtBQUssY0FBbkJ0QywwQ0FBQUEsb0JBQXFCcUMsTUFBTSxDQUFDLE9BQU07Ozs7Ozs4REFFdEUsOERBQUNFO29EQUFLaEIsV0FBVTs4REFBZXZCLEVBQUFBLGlCQUFBQSxRQUFRb0MsSUFBSSxjQUFacEMscUNBQUFBLGVBQWNZLElBQUksS0FBSTs7Ozs7Ozs7Ozs7O3dDQUd0RFQsZ0NBQ0MsOERBQUNxQjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNoQyxrREFBSUE7b0RBQ0hzQixNQUFLO29EQUNMVSxXQUFVO29EQUNWWSxTQUFTLElBQU0vQixrQkFBa0I7OERBQ2xDOzs7Ozs7OERBR0QsOERBQUNiLGtEQUFJQTtvREFDSHNCLE1BQUs7b0RBQ0xVLFdBQVU7b0RBQ1ZZLFNBQVMsSUFBTS9CLGtCQUFrQjs4REFDbEM7Ozs7Ozs4REFHRCw4REFBQ29DO29EQUFHakIsV0FBVTs7Ozs7OzhEQUNkLDhEQUFDVztvREFDQ0MsU0FBUzt3REFDUHJDLHdEQUFPQTt3REFDUE0sa0JBQWtCO29EQUNwQjtvREFDQW1CLFdBQVU7OERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU9QLDhEQUFDQztvQ0FBSUQsV0FBVTtvQ0FBV1UsS0FBS3hCOztzREFDN0IsOERBQUN5Qjs0Q0FDQ0MsU0FBUyxJQUFNN0Isc0JBQXNCLENBQUNEOzRDQUN0Q2tCLFdBQVU7NENBQ1ZrQixjQUFXO3NEQUVYLDRFQUFDQztnREFBSW5CLFdBQVU7Z0RBQVVvQixNQUFLO2dEQUFPQyxTQUFRO2dEQUFZQyxRQUFPOzBEQUM5RCw0RUFBQ0M7b0RBQ0NDLGVBQWM7b0RBQ2RDLGdCQUFlO29EQUNmQyxhQUFhO29EQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3dDQUtQN0Msb0NBQ0MsOERBQUNtQjs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNoQyxrREFBSUE7b0RBQ0hzQixNQUFLO29EQUNMVSxXQUFVO29EQUNWWSxTQUFTLElBQU03QixzQkFBc0I7OERBQ3RDOzs7Ozs7OERBR0QsOERBQUNmLGtEQUFJQTtvREFDSHNCLE1BQUs7b0RBQ0xVLFdBQVU7b0RBQ1ZZLFNBQVMsSUFBTTdCLHNCQUFzQjs4REFDdEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FVWCw4REFBQ2tCOzRCQUFJRCxXQUFVO3NDQUNiLDRFQUFDVztnQ0FDQ0MsU0FBUyxJQUFNakMsY0FBYyxDQUFDRDtnQ0FDOUJzQixXQUFVOzBDQUVWLDRFQUFDbUI7b0NBQUluQixXQUFVO29DQUFVb0IsTUFBSztvQ0FBT0MsU0FBUTtvQ0FBWUMsUUFBTzs4Q0FDN0Q1QywyQkFDQyw4REFBQzZDO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7a0VBRXJFLDhEQUFDSjt3Q0FBS0MsZUFBYzt3Q0FBUUMsZ0JBQWU7d0NBQVFDLGFBQWE7d0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFROUVqRCw0QkFDQyw4REFBQ3VCO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ087NEJBQUlQLFdBQVU7c0NBQ1paLGdCQUFnQm9CLEdBQUcsQ0FBQ0MsQ0FBQUEscUJBQ25CLDhEQUFDekMsa0RBQUlBO29DQUVIc0IsTUFBTW1CLEtBQUtuQixJQUFJO29DQUNmVSxXQUFVO29DQUNWWSxTQUFTLElBQU1qQyxjQUFjOzhDQUU1QjhCLEtBQUtwQixJQUFJO21DQUxMb0IsS0FBS3BCLElBQUk7Ozs7Ozs7Ozs7c0NBVXBCLDhEQUFDWTs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNoQyxrREFBSUE7b0NBQ0hzQixNQUFLO29DQUNMVSxXQUFVO29DQUNWWSxTQUFTLElBQU1qQyxjQUFjOzhDQUM5Qjs7Ozs7O2dDQUlBRix3QkFDQyw4REFBQ3dCO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDQztvREFBSUQsV0FBVTs4REFDWnZCLEVBQUFBLGlCQUFBQSxRQUFRb0MsSUFBSSxjQUFacEMsc0NBQUFBLHNCQUFBQSxlQUFjWSxJQUFJLGNBQWxCWiwwQ0FBQUEsb0JBQW9CcUMsTUFBTSxDQUFDLFNBQU1yQyxpQkFBQUEsUUFBUW9DLElBQUksY0FBWnBDLHNDQUFBQSx1QkFBQUEsZUFBY3NDLEtBQUssY0FBbkJ0QywyQ0FBQUEscUJBQXFCcUMsTUFBTSxDQUFDLE9BQU07Ozs7Ozs4REFFdEUsOERBQUNFO29EQUFLaEIsV0FBVTs4REFBNkJ2QixFQUFBQSxpQkFBQUEsUUFBUW9DLElBQUksY0FBWnBDLHFDQUFBQSxlQUFjWSxJQUFJLEtBQUk7Ozs7Ozs7Ozs7OztzREFFckUsOERBQUNyQixrREFBSUE7NENBQ0hzQixNQUFLOzRDQUNMVSxXQUFVOzRDQUNWWSxTQUFTLElBQU1qQyxjQUFjO3NEQUM5Qjs7Ozs7O3NEQUdELDhEQUFDWCxrREFBSUE7NENBQ0hzQixNQUFLOzRDQUNMVSxXQUFVOzRDQUNWWSxTQUFTLElBQU1qQyxjQUFjO3NEQUM5Qjs7Ozs7O3NEQUdELDhEQUFDZ0M7NENBQ0NDLFNBQVM7Z0RBQ1ByQyx3REFBT0E7Z0RBQ1BJLGNBQWM7NENBQ2hCOzRDQUNBcUIsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OERBS0gsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ1c7NENBQ0NDLFNBQVM7Z0RBQ1B0Qyx1REFBTUE7Z0RBQ05LLGNBQWM7NENBQ2hCOzRDQUNBcUIsV0FBVTtzREFDWDs7Ozs7O3NEQUdELDhEQUFDVzs0Q0FDQ0MsU0FBUztnREFDUHRDLHVEQUFNQTtnREFDTkssY0FBYzs0Q0FDaEI7NENBQ0FxQixXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVduQjtHQXJRTXhCOztRQUk4QkgsdURBQVVBOzs7S0FKeENHO0FBdVFOLGlFQUFlQSxNQUFNQSxFQUFBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxuYXZpZ2F0aW9uXFxIZWFkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xyXG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSdcclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IHVzZVNlc3Npb24sIHNpZ25Jbiwgc2lnbk91dCB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcclxuXHJcbmNvbnN0IEhlYWRlciA9ICgpID0+IHtcclxuICBjb25zdCBbaXNNZW51T3Blbiwgc2V0SXNNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbaXNVc2VyTWVudU9wZW4sIHNldElzVXNlck1lbnVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtpc0F1dGhEcm9wZG93bk9wZW4sIHNldElzQXV0aERyb3Bkb3duT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpXHJcbiAgY29uc3QgYXV0aERyb3Bkb3duUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKVxyXG4gIGNvbnN0IHVzZXJNZW51UmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKVxyXG5cclxuICBjb25zdCBuYXZpZ2F0aW9uSXRlbXMgPSBbXHJcbiAgICB7IG5hbWU6ICdBYm91dCBVcycsIGhyZWY6ICcvYWJvdXQnIH0sXHJcbiAgICB7IG5hbWU6ICdEZXZlbG9wbWVudHMnLCBocmVmOiAnL2RldmVsb3BtZW50cycgfSxcclxuICAgIHsgbmFtZTogJ0V4cGxvcmUgUGxhY2VzJywgaHJlZjogJy9leHBsb3JlJyB9LFxyXG4gICAgeyBuYW1lOiAnUHJvcGVydGllcycsIGhyZWY6ICcvcHJvcGVydGllcycgfSxcclxuICAgIHsgbmFtZTogJ1NlcnZpY2VzJywgaHJlZjogJy9zZXJ2aWNlcycgfSxcclxuICAgIHsgbmFtZTogJ0Jsb2cnLCBocmVmOiAnL2Jsb2cnIH0sXHJcbiAgICB7IG5hbWU6ICdDb250YWN0JywgaHJlZjogJy9jb250YWN0JyB9XHJcbiAgXVxyXG5cclxuICAvLyBIYW5kbGUgY2xpY2sgb3V0c2lkZSB0byBjbG9zZSBkcm9wZG93bnNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlQ2xpY2tPdXRzaWRlID0gKGV2ZW50OiBNb3VzZUV2ZW50KSA9PiB7XHJcbiAgICAgIGlmIChhdXRoRHJvcGRvd25SZWYuY3VycmVudCAmJiAhYXV0aERyb3Bkb3duUmVmLmN1cnJlbnQuY29udGFpbnMoZXZlbnQudGFyZ2V0IGFzIE5vZGUpKSB7XHJcbiAgICAgICAgc2V0SXNBdXRoRHJvcGRvd25PcGVuKGZhbHNlKVxyXG4gICAgICB9XHJcbiAgICAgIGlmICh1c2VyTWVudVJlZi5jdXJyZW50ICYmICF1c2VyTWVudVJlZi5jdXJyZW50LmNvbnRhaW5zKGV2ZW50LnRhcmdldCBhcyBOb2RlKSkge1xyXG4gICAgICAgIHNldElzVXNlck1lbnVPcGVuKGZhbHNlKVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKVxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgaGFuZGxlQ2xpY2tPdXRzaWRlKVxyXG4gICAgfVxyXG4gIH0sIFtdKVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGhlYWRlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3ctc20gYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwIHN0aWNreSB0b3AtMCB6LTUwXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBoLTE2IGxnOmgtMjBcIj5cclxuICAgICAgICAgIHsvKiBMb2dvICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XHJcbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgIHNyYz1cIi9pbWFnZXMvbG9nby5wbmdcIlxyXG4gICAgICAgICAgICAgICAgYWx0PVwiTVJIIFBsYXRmb3JtIExvZ29cIlxyXG4gICAgICAgICAgICAgICAgd2lkdGg9ezEyMH1cclxuICAgICAgICAgICAgICAgIGhlaWdodD17NDB9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggbGc6aC0xMCB3LWF1dG8gb2JqZWN0LWNvbnRhaW5cIlxyXG4gICAgICAgICAgICAgICAgcHJpb3JpdHlcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogRGVza3RvcCBOYXZpZ2F0aW9uICovfVxyXG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC04XCI+XHJcbiAgICAgICAgICAgIHtuYXZpZ2F0aW9uSXRlbXMubWFwKGl0ZW0gPT4gKFxyXG4gICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubmFtZX1cclxuICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1saW1lLTYwMCBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICApKX1cclxuICAgICAgICAgIDwvbmF2PlxyXG5cclxuICAgICAgICAgIHsvKiBEZXNrdG9wIEFjdGlvbnMgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBsZzpmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgey8qIExpc3QgUHJvcGVydHkgQnV0dG9uICovfVxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvbGlzdC1wcm9wZXJ0eVwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctbGltZS01MDAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLWxpbWUtNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBMaXN0IE15IFByb3BlcnR5XHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuXHJcbiAgICAgICAgICAgIHsvKiBVc2VyIEF1dGhlbnRpY2F0aW9uICovfVxyXG4gICAgICAgICAgICB7c3RhdHVzID09PSAnbG9hZGluZycgPyAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICkgOiBzZXNzaW9uID8gKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIiByZWY9e3VzZXJNZW51UmVmfT5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNVc2VyTWVudU9wZW4oIWlzVXNlck1lbnVPcGVuKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1saW1lLTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctbGltZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtzZXNzaW9uLnVzZXI/Lm5hbWU/LmNoYXJBdCgwKSB8fCBzZXNzaW9uLnVzZXI/LmVtYWlsPy5jaGFyQXQoMCkgfHwgJ1UnfVxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57c2Vzc2lvbi51c2VyPy5uYW1lIHx8ICdVc2VyJ308L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICAgICAgICB7aXNVc2VyTWVudU9wZW4gJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTAgbXQtMiB3LTQ4IGJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIGJvcmRlciBib3JkZXItZ3JheS0xMDAgcHktMiB6LTUwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzVXNlck1lbnVPcGVuKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBEYXNoYm9hcmRcclxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvcHJvZmlsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC00IHB5LTIgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1VzZXJNZW51T3BlbihmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgUHJvZmlsZVxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICA8aHIgY2xhc3NOYW1lPVwibXktMiBib3JkZXItZ3JheS0xMDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2lnbk91dCgpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldElzVXNlck1lbnVPcGVuKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCB0ZXh0LWxlZnQgcHgtNCBweS0yIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIFNpZ24gT3V0XHJcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCIgcmVmPXthdXRoRHJvcGRvd25SZWZ9PlxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0F1dGhEcm9wZG93bk9wZW4oIWlzQXV0aERyb3Bkb3duT3Blbil9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMTAgaC0xMCB0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtbGltZS02MDAgaG92ZXI6YmctZ3JheS01MCByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlVzZXIgYWNjb3VudCBtZW51XCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02XCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZD1cIk0xNiA3YTQgNCAwIDExLTggMCA0IDQgMCAwMTggMHpNMTIgMTRhNyA3IDAgMDAtNyA3aDE0YTcgNyAwIDAwLTctN3pcIlxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAge2lzQXV0aERyb3Bkb3duT3BlbiAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMCBtdC0yIHctNDggYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ncmF5LTEwMCBweS0yIHotNTBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zaWduaW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtNCBweS0yIHRleHQtZ3JheS03MDAgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNBdXRoRHJvcGRvd25PcGVuKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBTaWduIGluXHJcbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3NpZ251cFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC00IHB5LTIgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0F1dGhEcm9wZG93bk9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIENyZWF0ZSBuZXcgYWNjb3VudFxyXG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIE1vYmlsZSBtZW51IGJ1dHRvbiAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuXCI+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKCFpc01lbnVPcGVuKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtbGltZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiaC02IHctNlwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxyXG4gICAgICAgICAgICAgICAge2lzTWVudU9wZW4gPyAoXHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk00IDZoMTZNNCAxMmgxNk00IDE4aDE2XCIgLz5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBNb2JpbGUgTmF2aWdhdGlvbiAqL31cclxuICAgICAgICB7aXNNZW51T3BlbiAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiBib3JkZXItdCBib3JkZXItZ3JheS0xMDAgcHktNFwiPlxyXG4gICAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAge25hdmlnYXRpb25JdGVtcy5tYXAoaXRlbSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtbGltZS02MDAgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgIDwvbmF2PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMTAwXCI+XHJcbiAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgIGhyZWY9XCIvbGlzdC1wcm9wZXJ0eVwiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgYmctbGltZS01MDAgdGV4dC13aGl0ZSB0ZXh0LWNlbnRlciBweC02IHB5LTMgcm91bmRlZC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLWxpbWUtNjAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCBtYi00XCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIExpc3QgTXkgUHJvcGVydHlcclxuICAgICAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgICAgIHtzZXNzaW9uID8gKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctbGltZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge3Nlc3Npb24udXNlcj8ubmFtZT8uY2hhckF0KDApIHx8IHNlc3Npb24udXNlcj8uZW1haWw/LmNoYXJBdCgwKSB8fCAnVSd9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntzZXNzaW9uLnVzZXI/Lm5hbWUgfHwgJ1VzZXInfTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1saW1lLTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgRGFzaGJvYXJkXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICBocmVmPVwiL3Byb2ZpbGVcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1saW1lLTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgUHJvZmlsZVxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzaWduT3V0KClcclxuICAgICAgICAgICAgICAgICAgICAgIHNldElzTWVudU9wZW4oZmFsc2UpXHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtbGltZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIFNpZ24gT3V0XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzaWduSW4oKVxyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0SXNNZW51T3BlbihmYWxzZSlcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCB0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtbGltZS02MDAgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIHRleHQtbGVmdFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBMb2dpblxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNpZ25JbigpXHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRJc01lbnVPcGVuKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIGJnLWdyYXktOTAwIHRleHQtd2hpdGUgdGV4dC1jZW50ZXIgcHgtNCBweS0yIHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gaG92ZXI6YmctZ3JheS04MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIFJlZ2lzdGVyXHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvaGVhZGVyPlxyXG4gIClcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgSGVhZGVyXHJcbiJdLCJuYW1lcyI6WyJMaW5rIiwiSW1hZ2UiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsInVzZVNlc3Npb24iLCJzaWduSW4iLCJzaWduT3V0IiwiSGVhZGVyIiwic2Vzc2lvbiIsImlzTWVudU9wZW4iLCJzZXRJc01lbnVPcGVuIiwiaXNVc2VyTWVudU9wZW4iLCJzZXRJc1VzZXJNZW51T3BlbiIsImlzQXV0aERyb3Bkb3duT3BlbiIsInNldElzQXV0aERyb3Bkb3duT3BlbiIsImRhdGEiLCJzdGF0dXMiLCJhdXRoRHJvcGRvd25SZWYiLCJ1c2VyTWVudVJlZiIsIm5hdmlnYXRpb25JdGVtcyIsIm5hbWUiLCJocmVmIiwiaGFuZGxlQ2xpY2tPdXRzaWRlIiwiZXZlbnQiLCJjdXJyZW50IiwiY29udGFpbnMiLCJ0YXJnZXQiLCJkb2N1bWVudCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJwcmlvcml0eSIsIm5hdiIsIm1hcCIsIml0ZW0iLCJyZWYiLCJidXR0b24iLCJvbkNsaWNrIiwidXNlciIsImNoYXJBdCIsImVtYWlsIiwic3BhbiIsImhyIiwiYXJpYS1sYWJlbCIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/Header.tsx\n"));

/***/ })

});