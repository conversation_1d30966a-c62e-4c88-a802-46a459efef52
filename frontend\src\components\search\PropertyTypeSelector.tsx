'use client'

import { useState, useRef, useEffect } from 'react'

interface PropertyType {
  value: string
  label: string
  icon: string
  description?: string
}

interface PropertyTypeSelectorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  showIcons?: boolean
}

const PropertyTypeSelector = ({
  value,
  onChange,
  placeholder = 'Property Type',
  className = '',
  showIcons = true
}: PropertyTypeSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  const propertyTypes: PropertyType[] = [
    { value: '', label: 'All Property Types', icon: '🏢', description: 'Any type of property' },
    { value: 'house', label: 'House', icon: '🏠', description: 'Single-family homes' },
    { value: 'apartment', label: 'Apartment', icon: '🏢', description: 'Multi-unit residential buildings' },
    { value: 'condo', label: 'Condominium', icon: '🏙️', description: 'High-rise residential units' },
    { value: 'townhouse', label: 'Townhouse', icon: '🏘️', description: 'Multi-story attached homes' },
    { value: 'villa', label: 'Villa', icon: '🏛️', description: 'Luxury detached homes' },
    { value: 'studio', label: 'Studio', icon: '🏠', description: 'Single-room living spaces' },
    { value: 'penthouse', label: 'Penthouse', icon: '🏢', description: 'Top-floor luxury units' },
    { value: 'commercial', label: 'Commercial', icon: '🏬', description: 'Business and retail spaces' },
    { value: 'office', label: 'Office Space', icon: '🏢', description: 'Professional workspaces' },
    { value: 'retail', label: 'Retail Space', icon: '🏪', description: 'Shopping and commercial units' },
    { value: 'warehouse', label: 'Warehouse', icon: '🏭', description: 'Industrial storage facilities' },
    { value: 'land', label: 'Land/Lot', icon: '🌾', description: 'Vacant land for development' },
    { value: 'farm', label: 'Farm/Agricultural', icon: '🚜', description: 'Agricultural properties' }
  ]

  const selectedType = propertyTypes.find(type => type.value === value) || propertyTypes[0]

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSelect = (propertyType: PropertyType) => {
    onChange(propertyType.value)
    setIsOpen(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false)
      buttonRef.current?.blur()
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      setIsOpen(!isOpen)
    }
  }

  return (
    <div className={`relative ${className}`}>
      <button
        ref={buttonRef}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-lime-500 focus:border-transparent transition-all duration-200 text-gray-900 bg-white text-left flex items-center justify-between"
      >
        <div className="flex items-center">
          {showIcons && selectedType.icon && <span className="mr-3 text-lg">{selectedType.icon}</span>}
          <span className={selectedType.value ? 'text-gray-900' : 'text-gray-500'}>
            {selectedType.label || placeholder}
          </span>
        </div>
        <svg
          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto"
        >
          {propertyTypes.map(type => (
            <button
              key={type.value || 'all'}
              onClick={() => handleSelect(type)}
              className={`w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors duration-150 border-b border-gray-100 last:border-b-0 ${
                type.value === value ? 'bg-lime-50 text-lime-700' : 'text-gray-900'
              }`}
            >
              <div className="flex items-center">
                {showIcons && <span className="mr-3 text-lg">{type.icon}</span>}
                <div className="flex-1">
                  <div className="font-medium">{type.label}</div>
                  {type.description && <div className="text-sm text-gray-500 mt-1">{type.description}</div>}
                </div>
                {type.value === value && (
                  <svg className="h-5 w-5 text-lime-600" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}

export default PropertyTypeSelector
