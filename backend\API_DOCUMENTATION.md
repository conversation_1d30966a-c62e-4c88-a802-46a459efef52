# IAM Backend API Documentation

## Overview

This is a comprehensive Identity and Access Management (IAM) backend system built with Node.js, Express, and PostgreSQL. The system provides full user authentication, authorization, and permission management capabilities.

## Base URL

```
http://localhost:3001/api
```

## Authentication

All protected endpoints require a JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": boolean,
  "message": "string (optional)",
  "data": object | array,
  "error": "string (on error)",
  "type": "string (error type)",
  "timestamp": "ISO string",
  "requestId": "string"
}
```

## Error Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request / Validation Error
- `401` - Unauthorized / Authentication Required
- `403` - Forbidden / Access Denied
- `404` - Not Found
- `409` - Conflict / Duplicate Resource
- `413` - Request Entity Too Large
- `429` - Too Many Requests / Rate Limited
- `500` - Internal Server Error
- `503` - Service Unavailable

## Rate Limiting

- General API: 100 requests per 15 minutes per IP
- Authentication endpoints: 5 requests per 15 minutes per IP
- Password reset: 3 requests per hour per IP
- User management: 50 requests per 15 minutes per IP

## Endpoints

### Health Check

#### GET /health
Check server status.

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 12345
}
```

---

## Authentication Endpoints

### POST /api/auth/register
Register a new user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "SecurePass123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Validation Rules:**
- Email: Valid email format, max 255 characters
- Username: 3-30 characters, alphanumeric + underscores/hyphens only
- Password: Min 8 characters, must contain uppercase, lowercase, number, and special character
- First/Last Name: Optional, 1-50 characters, letters/spaces/apostrophes/hyphens only

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "firstName": "John",
      "lastName": "Doe",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "token": "jwt_token"
  }
}
```

### POST /api/auth/login
Authenticate user and get JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "firstName": "John",
      "lastName": "Doe"
    },
    "token": "jwt_token"
  }
}
```

### POST /api/auth/logout
Logout user (client-side token removal).

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### GET /api/auth/profile
Get current user profile.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "firstName": "John",
      "lastName": "Doe",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

---

## User Management Endpoints

### GET /api/users
Get all users with pagination and filtering.

**Headers:** `Authorization: Bearer <token>`
**Permissions Required:** `Users:read`

**Query Parameters:**
- `page` (optional): Page number (default: 1, max: 1000)
- `limit` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search term (max 100 characters)
- `isActive` (optional): Filter by active status (true/false)

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_id",
        "email": "<EMAIL>",
        "username": "username",
        "firstName": "John",
        "lastName": "Doe",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "groupMemberships": [
          {
            "group": {
              "id": "group_id",
              "name": "Users",
              "description": "Default user group"
            },
            "joinedAt": "2024-01-01T00:00:00.000Z"
          }
        ],
        "_count": {
          "groupMemberships": 1
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "pages": 5
    }
  }
}
```

### GET /api/users/:id
Get user by ID.

**Headers:** `Authorization: Bearer <token>`
**Permissions Required:** `Users:read`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "username",
      "firstName": "John",
      "lastName": "Doe",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "groupMemberships": [
        {
          "group": {
            "id": "group_id",
            "name": "Users",
            "description": "Default user group"
          },
          "joinedAt": "2024-01-01T00:00:00.000Z"
        }
      ]
    }
  }
}
```

### POST /api/users
Create a new user.

**Headers:** `Authorization: Bearer <token>`
**Permissions Required:** `Users:create`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "newuser",
  "password": "SecurePass123!",
  "firstName": "Jane",
  "lastName": "Smith",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "User created successfully",
  "data": {
    "user": {
      "id": "new_user_id",
      "email": "<EMAIL>",
      "username": "newuser",
      "firstName": "Jane",
      "lastName": "Smith",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

### PUT /api/users/:id
Update user information.

**Headers:** `Authorization: Bearer <token>`
**Permissions Required:** `Users:update`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "updateduser",
  "firstName": "Updated",
  "lastName": "Name",
  "isActive": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "User updated successfully",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "username": "updateduser",
      "firstName": "Updated",
      "lastName": "Name",
      "isActive": false,
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

### DELETE /api/users/:id
Delete a user.

**Headers:** `Authorization: Bearer <token>`
**Permissions Required:** `Users:delete`

**Response:**
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

### PUT /api/users/:id/password
Change user password.

**Headers:** `Authorization: Bearer <token>`
**Permissions Required:** `Users:update`

**Request Body:**
```json
{
  "currentPassword": "OldPass123!",
  "newPassword": "NewPass123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password updated successfully"
}
```

---

## Group Management Endpoints

### GET /api/groups
Get all groups with pagination and filtering.

**Headers:** `Authorization: Bearer <token>`
**Permissions Required:** `Groups:read`

**Query Parameters:**
- `page`, `limit`, `search`, `isActive` (same as users)

**Response:**
```json
{
  "success": true,
  "data": {
    "groups": [
      {
        "id": "group_id",
        "name": "Administrators",
        "description": "System administrators",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "members": [
          {
            "user": {
              "id": "user_id",
              "email": "<EMAIL>",
              "username": "admin"
            },
            "joinedAt": "2024-01-01T00:00:00.000Z"
          }
        ],
        "roles": [
          {
            "role": {
              "id": "role_id",
              "name": "Admin",
              "description": "Full system access"
            }
          }
        ],
        "_count": {
          "members": 1,
          "roles": 1
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 5,
      "pages": 1
    }
  }
}
```
