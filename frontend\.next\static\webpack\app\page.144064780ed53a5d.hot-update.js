"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/navigation/Header.tsx":
/*!**********************************************!*\
  !*** ./src/components/navigation/Header.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Header = ()=>{\n    var _session_user_name, _session_user, _session_user_email, _session_user1, _session_user2, _session_user_name1, _session_user3, _session_user_email1, _session_user4, _session_user5;\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isAuthDropdownOpen, setIsAuthDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const authDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const navigationItems = [\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Developments',\n            href: '/developments'\n        },\n        {\n            name: 'Explore Places',\n            href: '/explore'\n        },\n        {\n            name: 'Properties',\n            href: '/properties'\n        },\n        {\n            name: 'Services',\n            href: '/services'\n        },\n        {\n            name: 'Blog',\n            href: '/blog'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/images/logo.png\",\n                                    alt: \"MRH Platform Logo\",\n                                    width: 120,\n                                    height: 40,\n                                    className: \"h-8 lg:h-10 w-auto object-contain\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"bg-lime-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200\",\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                status === 'loading' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined) : session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                            className: \"flex items-center space-x-2 text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_name = _session_user.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.charAt(0)) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_email = _session_user1.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.charAt(0)) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: ((_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.name) || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/dashboard\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/profile\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    children: \"Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2 border-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)(),\n                                                    className: \"block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)(),\n                                            className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)(),\n                                            className: \"bg-gray-900 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-800 transition-colors duration-200\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-gray-100 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"block w-full bg-lime-500 text-white text-center px-6 py-3 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200 mb-4\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined),\n                                session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: ((_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : (_session_user_name1 = _session_user3.name) === null || _session_user_name1 === void 0 ? void 0 : _session_user_name1.charAt(0)) || ((_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : (_session_user_email1 = _session_user4.email) === null || _session_user_email1 === void 0 ? void 0 : _session_user_email1.charAt(0)) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: ((_session_user5 = session.user) === null || _session_user5 === void 0 ? void 0 : _session_user5.name) || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/profile\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block w-full text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200 text-left\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block w-full bg-gray-900 text-white text-center px-4 py-2 rounded-lg font-medium hover:bg-gray-800 transition-colors duration-200\",\n                                            children: \"Register\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"w9lAyWrSC0p3Z590IYNgfquraxs=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession\n    ];\n});\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/Header.tsx\n"));

/***/ })

});