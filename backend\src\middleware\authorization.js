import prisma from '../utils/database.js'
import logger from '../utils/logger.js'

/**
 * Get user permissions through group memberships
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Array of permissions
 */
const getUserPermissions = async userId => {
  try {
    const userWithPermissions = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        groupMemberships: {
          include: {
            group: {
              include: {
                roles: {
                  include: {
                    role: {
                      include: {
                        permissions: {
                          include: {
                            permission: {
                              include: {
                                module: true
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!userWithPermissions) {
      return []
    }

    // Flatten permissions from all groups and roles
    const permissions = []
    const permissionSet = new Set() // To avoid duplicates

    userWithPermissions.groupMemberships.forEach(membership => {
      membership.group.roles.forEach(groupRole => {
        groupRole.role.permissions.forEach(rolePermission => {
          const permission = rolePermission.permission
          const permissionKey = `${permission.module.name}:${permission.action}`

          if (!permissionSet.has(permissionKey)) {
            permissionSet.add(permissionKey)
            permissions.push({
              id: permission.id,
              action: permission.action,
              module: permission.module.name,
              moduleId: permission.moduleId,
              description: permission.description
            })
          }
        })
      })
    })

    return permissions
  } catch (error) {
    logger.error('Error getting user permissions:', error)
    return []
  }
}

/**
 * Check if user has specific permission
 * @param {string} userId - User ID
 * @param {string} module - Module name
 * @param {string} action - Action name (create, read, update, delete)
 * @returns {Promise<boolean>} Permission check result
 */
const hasPermission = async (userId, module, action) => {
  try {
    const permissions = await getUserPermissions(userId)
    return permissions.some(
      permission =>
        permission.module.toLowerCase() === module.toLowerCase() &&
        permission.action.toLowerCase() === action.toLowerCase()
    )
  } catch (error) {
    logger.error('Error checking permission:', error)
    return false
  }
}

/**
 * Middleware to check if user has required permission
 * @param {string} module - Module name
 * @param {string} action - Action name
 * @returns {Function} Express middleware function
 */
const checkPermission = (module, action) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        })
      }

      const hasAccess = await hasPermission(req.user.id, module, action)

      if (!hasAccess) {
        logger.warn(`Access denied for user ${req.user.email} to ${module}:${action}`)
        return res.status(403).json({
          success: false,
          error: `Access denied. Required permission: ${module}:${action}`
        })
      }

      next()
    } catch (error) {
      logger.error('Permission check error:', error)
      res.status(500).json({
        success: false,
        error: 'Permission check failed'
      })
    }
  }
}

/**
 * Middleware to check if user has any of the required permissions
 * @param {Array} permissions - Array of {module, action} objects
 * @returns {Function} Express middleware function
 */
const checkAnyPermission = permissions => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        })
      }

      let hasAccess = false
      for (const permission of permissions) {
        if (await hasPermission(req.user.id, permission.module, permission.action)) {
          hasAccess = true
          break
        }
      }

      if (!hasAccess) {
        const permissionStrings = permissions.map(p => `${p.module}:${p.action}`).join(', ')
        logger.warn(`Access denied for user ${req.user.email}. Required any of: ${permissionStrings}`)
        return res.status(403).json({
          success: false,
          error: `Access denied. Required any of: ${permissionStrings}`
        })
      }

      next()
    } catch (error) {
      logger.error('Permission check error:', error)
      res.status(500).json({
        success: false,
        error: 'Permission check failed'
      })
    }
  }
}

/**
 * Middleware to check if user has all required permissions
 * @param {Array} permissions - Array of {module, action} objects
 * @returns {Function} Express middleware function
 */
const checkAllPermissions = permissions => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        })
      }

      for (const permission of permissions) {
        const hasAccess = await hasPermission(req.user.id, permission.module, permission.action)
        if (!hasAccess) {
          logger.warn(`Access denied for user ${req.user.email} to ${permission.module}:${permission.action}`)
          return res.status(403).json({
            success: false,
            error: `Access denied. Required permission: ${permission.module}:${permission.action}`
          })
        }
      }

      next()
    } catch (error) {
      logger.error('Permission check error:', error)
      res.status(500).json({
        success: false,
        error: 'Permission check failed'
      })
    }
  }
}

/**
 * Check if user is admin (has admin role)
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} Admin check result
 */
const isAdmin = async userId => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        groupMemberships: {
          include: {
            group: {
              include: {
                roles: {
                  include: {
                    role: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!user) return false

    // Check if user has admin role through any group
    return user.groupMemberships.some(membership =>
      membership.group.roles.some(groupRole => groupRole.role.name.toLowerCase() === 'admin')
    )
  } catch (error) {
    logger.error('Error checking admin status:', error)
    return false
  }
}

/**
 * Middleware to check if user is admin
 */
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      })
    }

    const adminStatus = await isAdmin(req.user.id)

    if (!adminStatus) {
      logger.warn(`Admin access denied for user ${req.user.email}`)
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      })
    }

    next()
  } catch (error) {
    logger.error('Admin check error:', error)
    res.status(500).json({
      success: false,
      error: 'Admin check failed'
    })
  }
}

export {
  getUserPermissions,
  hasPermission,
  checkPermission,
  checkAnyPermission,
  checkAllPermissions,
  isAdmin,
  requireAdmin
}
