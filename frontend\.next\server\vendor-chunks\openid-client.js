/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split('.').map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, 'access_token', 'code', 'error_description', 'error_uri', 'error', 'expires_in', 'id_token', 'iss', 'response', 'session_state', 'state', 'token_type');\n}\nfunction authorizationHeaderValue(token, tokenType = 'Bearer') {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: 'openid',\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === 'claims' && typeof value === 'object') {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === 'resource' && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== 'string') {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !('kty' in k))) {\n        throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes('client_secret_post')) {\n                properties.token_endpoint_auth_method = 'client_secret_post';\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError('provide a redirect_uri or redirect_uris, not both');\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError('provide a response_type or response_types, not both');\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== 'string' || !metadata.client_id) {\n            throw new TypeError('client_id is required');\n        }\n        const properties = {\n            grant_types: [\n                'authorization_code'\n            ],\n            id_token_signed_response_alg: 'RS256',\n            authorization_signed_response_alg: 'RS256',\n            response_types: [\n                'code'\n            ],\n            token_endpoint_auth_method: 'client_secret_basic',\n            ...this.fapi1() ? {\n                grant_types: [\n                    'authorization_code',\n                    'implicit'\n                ],\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                response_types: [\n                    'code id_token'\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case 'self_signed_tls_client_auth':\n                case 'tls_client_auth':\n                    break;\n                case 'private_key_jwt':\n                    if (!jwks) {\n                        throw new TypeError('jwks is required');\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError('token_endpoint_auth_method is required');\n                default:\n                    throw new TypeError('invalid or unsupported token_endpoint_auth_method');\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport('token', this.issuer, properties);\n        [\n            'introspection',\n            'revocation'\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        assertIssuerConfiguration(this.issuer, 'authorization_endpoint');\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, '%20');\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join('\\n');\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, 'end_session_endpoint');\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === 'string';\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError('#callbackParams only accepts string urls, http.IncomingMessage or a lookalike');\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case 'GET':\n                    return pickCb(getSearchParams(input.url));\n                case 'POST':\n                    if (input.body === undefined) {\n                        throw new TypeError('incoming message body missing, include a body parser prior to this method call');\n                    }\n                    switch(typeof input.body){\n                        case 'object':\n                        case 'string':\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString('utf-8')));\n                            }\n                            if (typeof input.body === 'string') {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError('invalid IncomingMessage body object');\n                    }\n                default:\n                    throw new TypeError('invalid IncomingMessage method');\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            id_token: [\n                'id_token'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'authorization', checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'token', checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === 'string' && params.id_token.length) {\n            throw new RPError({\n                message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === 'string' && tokenset.id_token.length) {\n                throw new RPError({\n                    message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = 'A128CBC-HS256') {\n        const header = JSON.parse(base64url.decode(jwe.split('.')[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE enc received, expected %s, got: %s',\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: 'enc'\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === 'dir' ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: 'failed to decrypt JWE',\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === 'number' || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: 'missing required JWT property auth_time',\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== 'number') {\n                throw new RPError({\n                    message: 'JWT auth_time claim must be a JSON numeric value',\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === 'number' && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    'too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i',\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    'nonce mismatch, expected %s, got: %s',\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === 'authorization') {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: 'missing required property at_hash',\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: 'missing required property c_hash',\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: 'missing required property s_hash',\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: 's_hash',\n                        source: 'state'\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    'JWT issued too far in the past, now %i, iat %i',\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'at_hash',\n                    source: 'access_token'\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'c_hash',\n                    source: 'code'\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        'iss',\n        'sub',\n        'aud',\n        'exp',\n        'iat'\n    ]) {\n        const isSelfIssued = this.issuer.issuer === 'https://self-issued.me';\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    'failed to decode JWT (%s: %s)',\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWT alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                'sub_jwk'\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace('{tenantid}', payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        'unexpected iss value, expected %s, got: %s',\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== 'number') {\n                throw new RPError({\n                    message: 'JWT iat claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== 'number') {\n                throw new RPError({\n                    message: 'JWT nbf claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        'JWT not active yet, now %i, nbf %i',\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== 'number') {\n                throw new RPError({\n                    message: 'JWT exp claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        'JWT expired, now %i, exp %i',\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: 'missing required JWT property azp',\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            'aud is missing the client_id, expected %s to be included in %j',\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        'aud mismatch, expected %s, got: %s',\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === 'string') {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        'azp mismatch, got: %s',\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, 'public');\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: 'failed to use sub_jwk claim as an asymmetric JSON Web Key',\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: 'failed to match the subject with sub_jwk',\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith('HS')) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== 'none') {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: 'sig'\n            });\n        }\n        if (!keys && header.alg === 'none') {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: 'failed to validate JWT signature',\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError('refresh_token not present in TokenSet');\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: 'refresh_token',\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, 'token', skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            'sub mismatch, expected %s, got: %s',\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? 'DPoP' : accessToken instanceof TokenSet ? accessToken.token_type : 'Bearer' } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError('access_token not present in TokenSet');\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError('no access token provided');\n        } else if (typeof accessToken !== 'string') {\n            throw new TypeError('invalid access token provided');\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: 'buffer',\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers['www-authenticate'];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith('dpop ') && parseWwwAuthenticate(wwwAuthenticate).error === 'use_dpop_nonce') {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = 'GET', via = 'header', tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'userinfo_endpoint');\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== 'GET' && options.method !== 'POST') {\n            throw new TypeError('#userinfo() method can only be POST or a GET');\n        }\n        if (via === 'body' && options.method !== 'POST') {\n            throw new TypeError('can only send body on POST');\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: 'application/jwt'\n            };\n        } else {\n            options.headers = {\n                Accept: 'application/json'\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === 'body') {\n            options.headers.Authorization = undefined;\n            options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n            options.body = new URLSearchParams();\n            options.body.append('access_token', accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === 'GET') {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers['content-type'])) {\n                throw new RPError({\n                    message: 'expected application/jwt response from the userinfo_endpoint',\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: 'failed to parse userinfo JWE payload as JSON',\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, 'response', {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        'userinfo sub mismatch, expected %s, got: %s',\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? 'sha256' : len <= 384 ? 'sha384' : len <= 512 ? 'sha512' : false;\n        if (!hash) {\n            throw new Error('unsupported symmetric encryption key derivation');\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError('client_secret is required');\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const response = await authenticatedPost.call(this, 'token', {\n            form: body,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === 'use_dpop_nonce') {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'device_authorization_endpoint');\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, 'device_authorization', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'revocation_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'revocation', {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'introspection_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'introspection', {\n            form,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, 'registration_endpoint');\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: 'application/json',\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: 'json',\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: 'POST'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: 'GET',\n            url: registrationClientUri,\n            responseType: 'json',\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: 'application/json'\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || 'none', encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || 'A128CBC-HS256' } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError('requestObject must be a plain object');\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: 'oauth-authz-req+jwt'\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === 'none') {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                ''\n            ].join('.');\n        } else {\n            const symmetric = signingAlgorithm.startsWith('HS');\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: 'sig'\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: 'oauth-authz-req+jwt'\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: 'enc'\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === 'dir' ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'pushed_authorization_request_endpoint');\n        const body = {\n            ...'request' in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, 'pushed_authorization_request', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!('expires_in' in responseBody)) {\n            throw new RPError({\n                message: 'expected expires_in in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== 'number') {\n            throw new RPError({\n                message: 'invalid expires_in value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (!('request_uri' in responseBody)) {\n            throw new RPError({\n                message: 'expected request_uri in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== 'string') {\n            throw new RPError({\n                message: 'invalid request_uri value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === 'FAPI1Client';\n    }\n    fapi2() {\n        return this.constructor.name === 'FAPI2Client';\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            'iss',\n            'exp',\n            'aud'\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError('payload must be a plain object');\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === 'node:crypto') {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError('unrecognized crypto runtime');\n        }\n        if (privateKey.type !== 'private') {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError('could not determine DPoP JWS Algorithm');\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash('sha256').update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: 'dpop+jwt',\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        case 'ECDSA':\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case 'P-256':\n                        return 'ES256';\n                    case 'P-384':\n                        return 'ES384';\n                    case 'P-521':\n                        return 'ES512';\n                    default:\n                        break;\n                }\n                break;\n            }\n        case 'RSASSA-PKCS1-v1_5':\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case 'RSA-PSS':\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError('unsupported DPoP private key');\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === 'node:crypto') {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case 'ed25519':\n            case 'ed448':\n                return 'EdDSA';\n            case 'ec':\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case 'rsa':\n            case rsaPssParams && 'rsa-pss':\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError('unsupported DPoP private key');\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === 'object' && privateKeyInput.format === 'jwk' && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === 'rsa-pss') {\n                candidates = candidates.filter((value)=>value.startsWith('PS'));\n            }\n            return [\n                'PS256',\n                'PS384',\n                'PS512',\n                'RS256',\n                'RS384',\n                'RS384'\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return 'PS256';\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.key.crv){\n            case 'P-256':\n                return 'ES256';\n            case 'secp256k1':\n                return 'ES256K';\n            case 'P-384':\n                return 'ES384';\n            case 'P-512':\n                return 'ES512';\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: 'der',\n            type: 'pkcs8'\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return 'ES256';\n        }\n        if (curveOid.equals(p384)) {\n            return 'ES384';\n        }\n        if (curveOid.equals(p521)) {\n            return 'ES512';\n        }\n        if (curveOid.equals(secp256k1)) {\n            return 'ES256K';\n        }\n        throw new TypeError('unsupported DPoP private key curve');\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === 'node:crypto' && typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.format === 'jwk') {\n        return pick(privateKeyInput.key, 'kty', 'crv', 'x', 'y', 'e', 'n');\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), 'kty', 'crv', 'x', 'y', 'e', 'n');\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === 'WebCryptoAPI') {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\n\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass DeviceFlowHandle {\n  #aborted;\n  #client;\n  #clientAssertionPayload;\n  #DPoP;\n  #exchangeBody;\n  #expires_at;\n  #interval;\n  #maxAge;\n  #response;\n  constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }) {\n    ['verification_uri', 'user_code', 'device_code'].forEach((prop) => {\n      if (typeof response[prop] !== 'string' || !response[prop]) {\n        throw new RPError(\n          `expected ${prop} string to be returned by Device Authorization Response, got %j`,\n          response[prop],\n        );\n      }\n    });\n\n    if (!Number.isSafeInteger(response.expires_in)) {\n      throw new RPError(\n        'expected expires_in number to be returned by Device Authorization Response, got %j',\n        response.expires_in,\n      );\n    }\n\n    this.#expires_at = now() + response.expires_in;\n    this.#client = client;\n    this.#DPoP = DPoP;\n    this.#maxAge = maxAge;\n    this.#exchangeBody = exchangeBody;\n    this.#clientAssertionPayload = clientAssertionPayload;\n    this.#response = response;\n    this.#interval = response.interval * 1000 || 5000;\n  }\n\n  abort() {\n    this.#aborted = true;\n  }\n\n  async poll({ signal } = {}) {\n    if ((signal && signal.aborted) || this.#aborted) {\n      throw new RPError('polling aborted');\n    }\n\n    if (this.expired()) {\n      throw new RPError(\n        'the device code %j has expired and the device authorization session has concluded',\n        this.device_code,\n      );\n    }\n\n    await new Promise((resolve) => setTimeout(resolve, this.#interval));\n\n    let tokenset;\n    try {\n      tokenset = await this.#client.grant(\n        {\n          ...this.#exchangeBody,\n          grant_type: 'urn:ietf:params:oauth:grant-type:device_code',\n          device_code: this.device_code,\n        },\n        { clientAssertionPayload: this.#clientAssertionPayload, DPoP: this.#DPoP },\n      );\n    } catch (err) {\n      switch (err instanceof OPError && err.error) {\n        case 'slow_down':\n          this.#interval += 5000;\n        case 'authorization_pending':\n          return this.poll({ signal });\n        default:\n          throw err;\n      }\n    }\n\n    if ('id_token' in tokenset) {\n      await this.#client.decryptIdToken(tokenset);\n      await this.#client.validateIdToken(tokenset, undefined, 'token', this.#maxAge);\n    }\n\n    return tokenset;\n  }\n\n  get device_code() {\n    return this.#response.device_code;\n  }\n\n  get user_code() {\n    return this.#response.user_code;\n  }\n\n  get verification_uri() {\n    return this.#response.verification_uri;\n  }\n\n  get verification_uri_complete() {\n    return this.#response.verification_uri_complete;\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.#expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.#response, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { format } = __webpack_require__(/*! util */ \"util\");\n\nclass OPError extends Error {\n  constructor({ error_description, error, error_uri, session_state, state, scope }, response) {\n    super(!error_description ? error : `${error} (${error_description})`);\n\n    Object.assign(\n      this,\n      { error },\n      error_description && { error_description },\n      error_uri && { error_uri },\n      state && { state },\n      scope && { scope },\n      session_state && { session_state },\n    );\n\n    if (response) {\n      Object.defineProperty(this, 'response', {\n        value: response,\n      });\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nclass RPError extends Error {\n  constructor(...args) {\n    if (typeof args[0] === 'string') {\n      super(format(...args));\n    } else {\n      const { message, printf, response, ...rest } = args[0];\n      if (printf) {\n        super(format(...printf));\n      } else {\n        super(message);\n      }\n      Object.assign(this, rest);\n      if (response) {\n        Object.defineProperty(this, 'response', {\n          value: response,\n        });\n      }\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nmodule.exports = {\n  OPError,\n  RPError,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n  const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n\n  if (properties[eam] && properties[eam].endsWith('_jwt') && !properties[easa] && !issuer[easavs]) {\n    throw new TypeError(\n      `${easavs} must be configured on the issuer if ${easa} is not defined on a client`,\n    );\n  }\n}\n\nfunction assertIssuerConfiguration(issuer, endpoint) {\n  if (!issuer[endpoint]) {\n    throw new TypeError(`${endpoint} must be configured on the issuer`);\n  }\n}\n\nmodule.exports = {\n  assertSigningAlgValuesSupport,\n  assertIssuerConfiguration,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9hc3NlcnQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxpQkFBaUIsU0FBUzs7QUFFMUIsaUJBQWlCLFNBQVM7QUFDMUIsa0JBQWtCLFNBQVM7QUFDM0Isb0JBQW9CLFNBQVM7O0FBRTdCO0FBQ0E7QUFDQSxTQUFTLFFBQVEsc0NBQXNDLE1BQU07QUFDN0Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSwyQkFBMkIsVUFBVTtBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGFzc2VydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBhc3NlcnRTaWduaW5nQWxnVmFsdWVzU3VwcG9ydChlbmRwb2ludCwgaXNzdWVyLCBwcm9wZXJ0aWVzKSB7XG4gIGlmICghaXNzdWVyW2Ake2VuZHBvaW50fV9lbmRwb2ludGBdKSByZXR1cm47XG5cbiAgY29uc3QgZWFtID0gYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfbWV0aG9kYDtcbiAgY29uc3QgZWFzYSA9IGAke2VuZHBvaW50fV9lbmRwb2ludF9hdXRoX3NpZ25pbmdfYWxnYDtcbiAgY29uc3QgZWFzYXZzID0gYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfc2lnbmluZ19hbGdfdmFsdWVzX3N1cHBvcnRlZGA7XG5cbiAgaWYgKHByb3BlcnRpZXNbZWFtXSAmJiBwcm9wZXJ0aWVzW2VhbV0uZW5kc1dpdGgoJ19qd3QnKSAmJiAhcHJvcGVydGllc1tlYXNhXSAmJiAhaXNzdWVyW2Vhc2F2c10pIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFxuICAgICAgYCR7ZWFzYXZzfSBtdXN0IGJlIGNvbmZpZ3VyZWQgb24gdGhlIGlzc3VlciBpZiAke2Vhc2F9IGlzIG5vdCBkZWZpbmVkIG9uIGEgY2xpZW50YCxcbiAgICApO1xuICB9XG59XG5cbmZ1bmN0aW9uIGFzc2VydElzc3VlckNvbmZpZ3VyYXRpb24oaXNzdWVyLCBlbmRwb2ludCkge1xuICBpZiAoIWlzc3VlcltlbmRwb2ludF0pIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGAke2VuZHBvaW50fSBtdXN0IGJlIGNvbmZpZ3VyZWQgb24gdGhlIGlzc3VlcmApO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBhc3NlcnRTaWduaW5nQWxnVmFsdWVzU3VwcG9ydCxcbiAgYXNzZXJ0SXNzdWVyQ29uZmlndXJhdGlvbixcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("let encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input, encoding = 'utf8') => Buffer.from(input, encoding).toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input, encoding = 'utf8') =>\n    fromBase64(Buffer.from(input, encoding).toString('base64'));\n}\n\nconst decode = (input) => Buffer.from(input, 'base64');\n\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLHFCQUFxQjtBQUNyQixxQkFBcUIiLCJzb3VyY2VzIjpbIkU6XFxDb2RlXFxSZWFsaHViLVJlcG9zXFxtcmgtcGxhdGZvcm1cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcYmFzZTY0dXJsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImxldCBlbmNvZGU7XG5pZiAoQnVmZmVyLmlzRW5jb2RpbmcoJ2Jhc2U2NHVybCcpKSB7XG4gIGVuY29kZSA9IChpbnB1dCwgZW5jb2RpbmcgPSAndXRmOCcpID0+IEJ1ZmZlci5mcm9tKGlucHV0LCBlbmNvZGluZykudG9TdHJpbmcoJ2Jhc2U2NHVybCcpO1xufSBlbHNlIHtcbiAgY29uc3QgZnJvbUJhc2U2NCA9IChiYXNlNjQpID0+IGJhc2U2NC5yZXBsYWNlKC89L2csICcnKS5yZXBsYWNlKC9cXCsvZywgJy0nKS5yZXBsYWNlKC9cXC8vZywgJ18nKTtcbiAgZW5jb2RlID0gKGlucHV0LCBlbmNvZGluZyA9ICd1dGY4JykgPT5cbiAgICBmcm9tQmFzZTY0KEJ1ZmZlci5mcm9tKGlucHV0LCBlbmNvZGluZykudG9TdHJpbmcoJ2Jhc2U2NCcpKTtcbn1cblxuY29uc3QgZGVjb2RlID0gKGlucHV0KSA9PiBCdWZmZXIuZnJvbShpbnB1dCwgJ2Jhc2U2NCcpO1xuXG5tb2R1bGUuZXhwb3J0cy5kZWNvZGUgPSBkZWNvZGU7XG5tb2R1bGUuZXhwb3J0cy5lbmNvZGUgPSBlbmNvZGU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value) => encodeURIComponent(value).replace(/%20/g, '+');\n\nasync function clientAssertion(endpoint, payload) {\n  let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n  if (!alg) {\n    assertIssuerConfiguration(\n      this.issuer,\n      `${endpoint}_endpoint_auth_signing_alg_values_supported`,\n    );\n  }\n\n  if (this[`${endpoint}_endpoint_auth_method`] === 'client_secret_jwt') {\n    if (!alg) {\n      const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n      alg =\n        Array.isArray(supported) && supported.find((signAlg) => /^HS(?:256|384|512)/.test(signAlg));\n    }\n\n    if (!alg) {\n      throw new RPError(\n        `failed to determine a JWS Algorithm to use for ${\n          this[`${endpoint}_endpoint_auth_method`]\n        } Client Assertion`,\n      );\n    }\n\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n      .setProtectedHeader({ alg })\n      .sign(this.secretForAlg(alg));\n  }\n\n  const keystore = await keystores.get(this);\n\n  if (!keystore) {\n    throw new TypeError('no client jwks provided for signing a client assertion with');\n  }\n\n  if (!alg) {\n    const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n    alg =\n      Array.isArray(supported) &&\n      supported.find((signAlg) => keystore.get({ alg: signAlg, use: 'sig' }));\n  }\n\n  if (!alg) {\n    throw new RPError(\n      `failed to determine a JWS Algorithm to use for ${\n        this[`${endpoint}_endpoint_auth_method`]\n      } Client Assertion`,\n    );\n  }\n\n  const key = keystore.get({ alg, use: 'sig' });\n  if (!key) {\n    throw new RPError(\n      `no key found in client jwks to sign a client assertion with using alg ${alg}`,\n    );\n  }\n\n  return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n    .setProtectedHeader({ alg, kid: key.jwk && key.jwk.kid })\n    .sign(await key.keyObject(alg));\n}\n\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n  const authMethod = this[`${endpoint}_endpoint_auth_method`];\n  switch (authMethod) {\n    case 'self_signed_tls_client_auth':\n    case 'tls_client_auth':\n    case 'none':\n      return { form: { client_id: this.client_id } };\n    case 'client_secret_post':\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_post client authentication method requires a client_secret',\n        );\n      }\n      return { form: { client_id: this.client_id, client_secret: this.client_secret } };\n    case 'private_key_jwt':\n    case 'client_secret_jwt': {\n      const timestamp = now();\n\n      const assertion = await clientAssertion.call(this, endpoint, {\n        iat: timestamp,\n        exp: timestamp + 60,\n        jti: random(),\n        iss: this.client_id,\n        sub: this.client_id,\n        aud: this.issuer.issuer,\n        ...clientAssertionPayload,\n      });\n\n      return {\n        form: {\n          client_id: this.client_id,\n          client_assertion: assertion,\n          client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',\n        },\n      };\n    }\n    case 'client_secret_basic': {\n      // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n      // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n      // > The client identifier is encoded using the\n      // > \"application/x-www-form-urlencoded\" encoding algorithm per\n      // > Appendix B, and the encoded value is used as the username; the client\n      // > password is encoded using the same algorithm and used as the\n      // > password.\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_basic client authentication method requires a client_secret',\n        );\n      }\n      const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n      const value = Buffer.from(encoded).toString('base64');\n      return { headers: { Authorization: `Basic ${value}` } };\n    }\n    default: {\n      throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n    }\n  }\n}\n\nfunction resolveResponseType() {\n  const { length, 0: value } = this.response_types;\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nfunction resolveRedirectUri() {\n  const { length, 0: value } = this.redirect_uris || [];\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nasync function authenticatedPost(\n  endpoint,\n  opts,\n  { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {},\n) {\n  const auth = await authFor.call(this, endpointAuthMethod, { clientAssertionPayload });\n  const requestOpts = merge(opts, auth);\n\n  const mTLS =\n    this[`${endpointAuthMethod}_endpoint_auth_method`].includes('tls_client_auth') ||\n    (endpoint === 'token' && this.tls_client_certificate_bound_access_tokens);\n\n  let targetUrl;\n  if (mTLS && this.issuer.mtls_endpoint_aliases) {\n    targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n  }\n\n  targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n\n  if ('form' in requestOpts) {\n    for (const [key, value] of Object.entries(requestOpts.form)) {\n      if (typeof value === 'undefined') {\n        delete requestOpts.form[key];\n      }\n    }\n  }\n\n  return request.call(\n    this,\n    {\n      ...requestOpts,\n      method: 'POST',\n      url: targetUrl,\n      headers: {\n        ...(endpoint !== 'revocation'\n          ? {\n              Accept: 'application/json',\n            }\n          : undefined),\n        ...requestOpts.headers,\n      },\n    },\n    { mTLS, DPoP },\n  );\n}\n\nmodule.exports = {\n  resolveResponseType,\n  resolveRedirectUri,\n  authFor,\n  authenticatedPost,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\n\nmodule.exports = {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRTpcXENvZGVcXFJlYWxodWItUmVwb3NcXG1yaC1wbGF0Zm9ybVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxjb25zdHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgSFRUUF9PUFRJT05TID0gU3ltYm9sKCk7XG5jb25zdCBDTE9DS19UT0xFUkFOQ0UgPSBTeW1ib2woKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIENMT0NLX1RPTEVSQU5DRSxcbiAgSFRUUF9PUFRJT05TLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nmodule.exports = (token) => {\n  if (typeof token !== 'string' || !token) {\n    throw new TypeError('JWT must be a string');\n  }\n\n  const { 0: header, 1: payload, 2: signature, length } = token.split('.');\n\n  if (length === 5) {\n    throw new TypeError('encrypted JWTs cannot be decoded');\n  }\n\n  if (length !== 3) {\n    throw new Error('JWTs must have three components');\n  }\n\n  try {\n    return {\n      header: JSON.parse(base64url.decode(header)),\n      payload: JSON.parse(base64url.decode(payload)),\n      signature,\n    };\n  } catch (err) {\n    throw new Error('JWT is malformed');\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWNvZGVfand0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLGdGQUFhOztBQUV2QztBQUNBO0FBQ0E7QUFDQTs7QUFFQSxVQUFVLDhDQUE4Qzs7QUFFeEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGRlY29kZV9qd3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYmFzZTY0dXJsID0gcmVxdWlyZSgnLi9iYXNlNjR1cmwnKTtcblxubW9kdWxlLmV4cG9ydHMgPSAodG9rZW4pID0+IHtcbiAgaWYgKHR5cGVvZiB0b2tlbiAhPT0gJ3N0cmluZycgfHwgIXRva2VuKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignSldUIG11c3QgYmUgYSBzdHJpbmcnKTtcbiAgfVxuXG4gIGNvbnN0IHsgMDogaGVhZGVyLCAxOiBwYXlsb2FkLCAyOiBzaWduYXR1cmUsIGxlbmd0aCB9ID0gdG9rZW4uc3BsaXQoJy4nKTtcblxuICBpZiAobGVuZ3RoID09PSA1KSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignZW5jcnlwdGVkIEpXVHMgY2Fubm90IGJlIGRlY29kZWQnKTtcbiAgfVxuXG4gIGlmIChsZW5ndGggIT09IDMpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0pXVHMgbXVzdCBoYXZlIHRocmVlIGNvbXBvbmVudHMnKTtcbiAgfVxuXG4gIHRyeSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGhlYWRlcjogSlNPTi5wYXJzZShiYXNlNjR1cmwuZGVjb2RlKGhlYWRlcikpLFxuICAgICAgcGF5bG9hZDogSlNPTi5wYXJzZShiYXNlNjR1cmwuZGVjb2RlKHBheWxvYWQpKSxcbiAgICAgIHNpZ25hdHVyZSxcbiAgICB9O1xuICB9IGNhdGNoIChlcnIpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0pXVCBpcyBtYWxmb3JtZWQnKTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = globalThis.structuredClone || ((obj) => JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGRlZXBfY2xvbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBnbG9iYWxUaGlzLnN0cnVjdHVyZWRDbG9uZSB8fCAoKG9iaikgPT4gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShvYmopKSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction defaults(deep, target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (typeof target[key] === 'undefined' && typeof value !== 'undefined') {\n        target[key] = value;\n      }\n\n      if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n        defaults(true, target[key], value);\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWZhdWx0cy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLG1CQUFtQiIsInNvdXJjZXMiOlsiRTpcXENvZGVcXFJlYWxodWItUmVwb3NcXG1yaC1wbGF0Zm9ybVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxkZWZhdWx0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1BsYWluT2JqZWN0ID0gcmVxdWlyZSgnLi9pc19wbGFpbl9vYmplY3QnKTtcblxuZnVuY3Rpb24gZGVmYXVsdHMoZGVlcCwgdGFyZ2V0LCAuLi5zb3VyY2VzKSB7XG4gIGZvciAoY29uc3Qgc291cmNlIG9mIHNvdXJjZXMpIHtcbiAgICBpZiAoIWlzUGxhaW5PYmplY3Qoc291cmNlKSkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHNvdXJjZSkpIHtcbiAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBpZiAqL1xuICAgICAgaWYgKGtleSA9PT0gJ19fcHJvdG9fXycgfHwga2V5ID09PSAnY29uc3RydWN0b3InKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuICAgICAgaWYgKHR5cGVvZiB0YXJnZXRba2V5XSA9PT0gJ3VuZGVmaW5lZCcgJiYgdHlwZW9mIHZhbHVlICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICB0YXJnZXRba2V5XSA9IHZhbHVlO1xuICAgICAgfVxuXG4gICAgICBpZiAoZGVlcCAmJiBpc1BsYWluT2JqZWN0KHRhcmdldFtrZXldKSAmJiBpc1BsYWluT2JqZWN0KHZhbHVlKSkge1xuICAgICAgICBkZWZhdWx0cyh0cnVlLCB0YXJnZXRba2V5XSwgdmFsdWUpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB0YXJnZXQ7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gZGVmYXVsdHMuYmluZCh1bmRlZmluZWQsIGZhbHNlKTtcbm1vZHVsZS5leHBvcnRzLmRlZXAgPSBkZWZhdWx0cy5iaW5kKHVuZGVmaW5lZCwgdHJ1ZSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nconst random = (bytes = 32) => base64url.encode(randomBytes(bytes));\n\nmodule.exports = {\n  random,\n  state: random,\n  nonce: random,\n  codeVerifier: random,\n  codeChallenge: (codeVerifier) =>\n    base64url.encode(createHash('sha256').update(codeVerifier).digest()),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsMEJBQTBCLEVBQUUsbUJBQU8sQ0FBQyxzQkFBUTs7QUFFcEQsa0JBQWtCLG1CQUFPLENBQUMsZ0ZBQWE7O0FBRXZDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFxDb2RlXFxSZWFsaHViLVJlcG9zXFxtcmgtcGxhdGZvcm1cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcZ2VuZXJhdG9ycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IGNyZWF0ZUhhc2gsIHJhbmRvbUJ5dGVzIH0gPSByZXF1aXJlKCdjcnlwdG8nKTtcblxuY29uc3QgYmFzZTY0dXJsID0gcmVxdWlyZSgnLi9iYXNlNjR1cmwnKTtcblxuY29uc3QgcmFuZG9tID0gKGJ5dGVzID0gMzIpID0+IGJhc2U2NHVybC5lbmNvZGUocmFuZG9tQnl0ZXMoYnl0ZXMpKTtcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIHJhbmRvbSxcbiAgc3RhdGU6IHJhbmRvbSxcbiAgbm9uY2U6IHJhbmRvbSxcbiAgY29kZVZlcmlmaWVyOiByYW5kb20sXG4gIGNvZGVDaGFsbGVuZ2U6IChjb2RlVmVyaWZpZXIpID0+XG4gICAgYmFzZTY0dXJsLmVuY29kZShjcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoY29kZVZlcmlmaWVyKS5kaWdlc3QoKSksXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\nmodule.exports = util.types.isKeyObject || ((obj) => obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxrQkFBTTtBQUMzQixlQUFlLG1CQUFPLENBQUMsc0JBQVE7O0FBRS9CIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGlzX2tleV9vYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdXRpbCA9IHJlcXVpcmUoJ3V0aWwnKTtcbmNvbnN0IGNyeXB0byA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHV0aWwudHlwZXMuaXNLZXlPYmplY3QgfHwgKChvYmopID0+IG9iaiAmJiBvYmogaW5zdGFuY2VvZiBjcnlwdG8uS2V5T2JqZWN0KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("module.exports = (a) => !!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIkU6XFxDb2RlXFxSZWFsaHViLVJlcG9zXFxtcmgtcGxhdGZvcm1cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcaXNfcGxhaW5fb2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gKGEpID0+ICEhYSAmJiBhLmNvbnN0cnVjdG9yID09PSBPYmplY3Q7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx) => {\n  if (!caches.has(ctx)) {\n    caches.set(ctx, new LRU({ max: 100 }));\n  }\n  return caches.get(ctx);\n};\n\nasync function getKeyStore(reload = false) {\n  assertIssuerConfiguration(this, 'jwks_uri');\n\n  const keystore = keystores.get(this);\n  const cache = lrus(this);\n\n  if (reload || !keystore) {\n    if (inFlight.has(this)) {\n      return inFlight.get(this);\n    }\n    cache.reset();\n    inFlight.set(\n      this,\n      (async () => {\n        const response = await request\n          .call(this, {\n            method: 'GET',\n            responseType: 'json',\n            url: this.jwks_uri,\n            headers: {\n              Accept: 'application/json, application/jwk-set+json',\n            },\n          })\n          .finally(() => {\n            inFlight.delete(this);\n          });\n        const jwks = processResponse(response);\n\n        const joseKeyStore = KeyStore.fromJWKS(jwks, { onlyPublic: true });\n        cache.set('throttle', true, 60 * 1000);\n        keystores.set(this, joseKeyStore);\n\n        return joseKeyStore;\n      })(),\n    );\n\n    return inFlight.get(this);\n  }\n\n  return keystore;\n}\n\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n  const cache = lrus(this);\n\n  const def = {\n    kid,\n    kty,\n    alg,\n    use,\n  };\n\n  const defHash = objectHash(def, {\n    algorithm: 'sha256',\n    ignoreUnknown: true,\n    unorderedArrays: true,\n    unorderedSets: true,\n    respectType: false,\n  });\n\n  // refresh keystore on every unknown key but also only upto once every minute\n  const freshJwksUri = cache.get(defHash) || cache.get('throttle');\n\n  const keystore = await getKeyStore.call(this, !freshJwksUri);\n  const keys = keystore.all(def);\n\n  delete def.use;\n  if (keys.length === 0) {\n    throw new RPError({\n      printf: [\"no valid key found in issuer's jwks_uri for key parameters %j\", def],\n      jwks: keystore,\n    });\n  }\n\n  if (!allowMulti && keys.length > 1 && !kid) {\n    throw new RPError({\n      printf: [\n        \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n        def,\n      ],\n      jwks: keystore,\n    });\n  }\n\n  cache.set(defHash, true);\n\n  return keys;\n}\n\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nconst internal = Symbol();\n\nconst keyscore = (key, { alg, use }) => {\n  let score = 0;\n\n  if (alg && key.alg) {\n    score++;\n  }\n\n  if (use && key.use) {\n    score++;\n  }\n\n  return score;\n};\n\nfunction getKtyFromAlg(alg) {\n  switch (typeof alg === 'string' && alg.slice(0, 2)) {\n    case 'RS':\n    case 'PS':\n      return 'RSA';\n    case 'ES':\n      return 'EC';\n    case 'Ed':\n      return 'OKP';\n    default:\n      return undefined;\n  }\n}\n\nfunction getAlgorithms(use, alg, kty, crv) {\n  // Ed25519, Ed448, and secp256k1 always have \"alg\"\n  // OKP always has \"use\"\n  if (alg) {\n    return new Set([alg]);\n  }\n\n  switch (kty) {\n    case 'EC': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n      }\n\n      if (use === 'sig' || use === undefined) {\n        switch (crv) {\n          case 'P-256':\n          case 'P-384':\n            algs = algs.concat([`ES${crv.slice(-3)}`]);\n            break;\n          case 'P-521':\n            algs = algs.concat(['ES512']);\n            break;\n          case 'secp256k1':\n            if (jose.cryptoRuntime === 'node:crypto') {\n              algs = algs.concat(['ES256K']);\n            }\n            break;\n        }\n      }\n\n      return new Set(algs);\n    }\n    case 'OKP': {\n      return new Set(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n    }\n    case 'RSA': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['RSA-OAEP', 'RSA-OAEP-256', 'RSA-OAEP-384', 'RSA-OAEP-512']);\n        if (jose.cryptoRuntime === 'node:crypto') {\n          algs = algs.concat(['RSA1_5']);\n        }\n      }\n\n      if (use === 'sig' || use === undefined) {\n        algs = algs.concat(['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS512']);\n      }\n\n      return new Set(algs);\n    }\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nmodule.exports = class KeyStore {\n  #keys;\n\n  constructor(i, keys) {\n    if (i !== internal) throw new Error('invalid constructor call');\n    this.#keys = keys;\n  }\n\n  toJWKS() {\n    return {\n      keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } }) => jwk),\n    };\n  }\n\n  all({ alg, kid, use } = {}) {\n    if (!use || !alg) {\n      throw new Error();\n    }\n\n    const kty = getKtyFromAlg(alg);\n\n    const search = { alg, use };\n    return this.filter((key) => {\n      let candidate = true;\n\n      if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n        candidate = false;\n      }\n\n      if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n        candidate = false;\n      }\n\n      if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n        candidate = false;\n      }\n\n      if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n        candidate = false;\n      } else if (!key.algorithms.has(alg)) {\n        candidate = false;\n      }\n\n      return candidate;\n    }).sort((first, second) => keyscore(second, search) - keyscore(first, search));\n  }\n\n  get(...args) {\n    return this.all(...args)[0];\n  }\n\n  static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n    if (\n      !isPlainObject(jwks) ||\n      !Array.isArray(jwks.keys) ||\n      jwks.keys.some((k) => !isPlainObject(k) || !('kty' in k))\n    ) {\n      throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n\n    const keys = [];\n\n    for (let jwk of jwks.keys) {\n      jwk = clone(jwk);\n      const { kty, kid, crv } = jwk;\n\n      let { alg, use } = jwk;\n\n      if (typeof kty !== 'string' || !kty) {\n        continue;\n      }\n\n      if (use !== undefined && use !== 'sig' && use !== 'enc') {\n        continue;\n      }\n\n      if (typeof alg !== 'string' && alg !== undefined) {\n        continue;\n      }\n\n      if (typeof kid !== 'string' && kid !== undefined) {\n        continue;\n      }\n\n      if (kty === 'EC' && use === 'sig') {\n        switch (crv) {\n          case 'P-256':\n            alg = 'ES256';\n            break;\n          case 'P-384':\n            alg = 'ES384';\n            break;\n          case 'P-521':\n            alg = 'ES512';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (crv === 'secp256k1') {\n        use = 'sig';\n        alg = 'ES256K';\n      }\n\n      if (kty === 'OKP') {\n        switch (crv) {\n          case 'Ed25519':\n          case 'Ed448':\n            use = 'sig';\n            alg = 'EdDSA';\n            break;\n          case 'X25519':\n          case 'X448':\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (alg && !use) {\n        switch (true) {\n          case alg.startsWith('ECDH'):\n            use = 'enc';\n            break;\n          case alg.startsWith('RSA'):\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (onlyPrivate && (jwk.kty === 'oct' || !jwk.d)) {\n        throw new Error('jwks must only contain private keys');\n      }\n\n      if (onlyPublic && (jwk.d || jwk.k)) {\n        continue;\n      }\n\n      keys.push({\n        jwk: { ...jwk, alg, use },\n        async keyObject(alg) {\n          if (this[alg]) {\n            return this[alg];\n          }\n\n          const keyObject = await jose.importJWK(this.jwk, alg);\n          this[alg] = keyObject;\n          return keyObject;\n        },\n        get algorithms() {\n          Object.defineProperty(this, 'algorithms', {\n            value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n            enumerable: true,\n            configurable: false,\n          });\n          return this.algorithms;\n        },\n      });\n    }\n\n    return new this(internal, keys);\n  }\n\n  filter(...args) {\n    return this.#keys.filter(...args);\n  }\n\n  find(...args) {\n    return this.#keys.find(...args);\n  }\n\n  every(...args) {\n    return this.#keys.every(...args);\n  }\n\n  some(...args) {\n    return this.#keys.some(...args);\n  }\n\n  map(...args) {\n    return this.#keys.map(...args);\n  }\n\n  forEach(...args) {\n    return this.#keys.forEach(...args);\n  }\n\n  reduce(...args) {\n    return this.#keys.reduce(...args);\n  }\n\n  sort(...args) {\n    return this.#keys.sort(...args);\n  }\n\n  *[Symbol.iterator]() {\n    for (const key of this.#keys) {\n      yield key;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction merge(target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (isPlainObject(target[key]) && isPlainObject(value)) {\n        target[key] = merge(target[key], value);\n      } else if (typeof value !== 'undefined') {\n        target[key] = value;\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXG1lcmdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUGxhaW5PYmplY3QgPSByZXF1aXJlKCcuL2lzX3BsYWluX29iamVjdCcpO1xuXG5mdW5jdGlvbiBtZXJnZSh0YXJnZXQsIC4uLnNvdXJjZXMpIHtcbiAgZm9yIChjb25zdCBzb3VyY2Ugb2Ygc291cmNlcykge1xuICAgIGlmICghaXNQbGFpbk9iamVjdChzb3VyY2UpKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoc291cmNlKSkge1xuICAgICAgLyogaXN0YW5idWwgaWdub3JlIGlmICovXG4gICAgICBpZiAoa2V5ID09PSAnX19wcm90b19fJyB8fCBrZXkgPT09ICdjb25zdHJ1Y3RvcicpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBpZiAoaXNQbGFpbk9iamVjdCh0YXJnZXRba2V5XSkgJiYgaXNQbGFpbk9iamVjdCh2YWx1ZSkpIHtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSBtZXJnZSh0YXJnZXRba2V5XSwgdmFsdWUpO1xuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgdmFsdWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gdmFsdWU7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBtZXJnZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("module.exports = function pick(object, ...paths) {\n  const obj = {};\n  for (const path of paths) {\n    if (object[path] !== undefined) {\n      obj[path] = object[path];\n    }\n  }\n  return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRTpcXENvZGVcXFJlYWxodWItUmVwb3NcXG1yaC1wbGF0Zm9ybVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxwaWNrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gcGljayhvYmplY3QsIC4uLnBhdGhzKSB7XG4gIGNvbnN0IG9iaiA9IHt9O1xuICBmb3IgKGNvbnN0IHBhdGggb2YgcGF0aHMpIHtcbiAgICBpZiAob2JqZWN0W3BhdGhdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIG9ialtwYXRoXSA9IG9iamVjdFtwYXRoXTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG9iajtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\n\nconst throwAuthenticateErrors = (response) => {\n  const params = parseWwwAuthenticate(response.headers['www-authenticate']);\n\n  if (params.error) {\n    throw new OPError(params, response);\n  }\n};\n\nconst isStandardBodyError = (response) => {\n  let result = false;\n  try {\n    let jsonbody;\n    if (typeof response.body !== 'object' || Buffer.isBuffer(response.body)) {\n      jsonbody = JSON.parse(response.body);\n    } else {\n      jsonbody = response.body;\n    }\n    result = typeof jsonbody.error === 'string' && jsonbody.error.length;\n    if (result) Object.defineProperty(response, 'body', { value: jsonbody, configurable: true });\n  } catch (err) {}\n\n  return result;\n};\n\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n  if (response.statusCode !== statusCode) {\n    if (bearer) {\n      throwAuthenticateErrors(response);\n    }\n\n    if (isStandardBodyError(response)) {\n      throw new OPError(response.body, response);\n    }\n\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s, got: %i %s',\n          statusCode,\n          STATUS_CODES[statusCode],\n          response.statusCode,\n          STATUS_CODES[response.statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  if (body && !response.body) {\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s with body but no body was returned',\n          statusCode,\n          STATUS_CODES[statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  return response.body;\n}\n\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9wcm9jZXNzX3Jlc3BvbnNlLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsZUFBZSxFQUFFLG1CQUFPLENBQUMsa0JBQU07QUFDdkMsUUFBUSxTQUFTLEVBQUUsbUJBQU8sQ0FBQyxrQkFBTTs7QUFFakMsUUFBUSxVQUFVLEVBQUUsbUJBQU8sQ0FBQyxtRUFBVztBQUN2Qyw2QkFBNkIsbUJBQU8sQ0FBQyw0R0FBMkI7O0FBRWhFO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCxxQ0FBcUM7QUFDL0YsSUFBSTs7QUFFSjtBQUNBOztBQUVBLHFDQUFxQyxnREFBZ0QsSUFBSTtBQUN6RjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHByb2Nlc3NfcmVzcG9uc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBTVEFUVVNfQ09ERVMgfSA9IHJlcXVpcmUoJ2h0dHAnKTtcbmNvbnN0IHsgZm9ybWF0IH0gPSByZXF1aXJlKCd1dGlsJyk7XG5cbmNvbnN0IHsgT1BFcnJvciB9ID0gcmVxdWlyZSgnLi4vZXJyb3JzJyk7XG5jb25zdCBwYXJzZVd3d0F1dGhlbnRpY2F0ZSA9IHJlcXVpcmUoJy4vd3d3X2F1dGhlbnRpY2F0ZV9wYXJzZXInKTtcblxuY29uc3QgdGhyb3dBdXRoZW50aWNhdGVFcnJvcnMgPSAocmVzcG9uc2UpID0+IHtcbiAgY29uc3QgcGFyYW1zID0gcGFyc2VXd3dBdXRoZW50aWNhdGUocmVzcG9uc2UuaGVhZGVyc1snd3d3LWF1dGhlbnRpY2F0ZSddKTtcblxuICBpZiAocGFyYW1zLmVycm9yKSB7XG4gICAgdGhyb3cgbmV3IE9QRXJyb3IocGFyYW1zLCByZXNwb25zZSk7XG4gIH1cbn07XG5cbmNvbnN0IGlzU3RhbmRhcmRCb2R5RXJyb3IgPSAocmVzcG9uc2UpID0+IHtcbiAgbGV0IHJlc3VsdCA9IGZhbHNlO1xuICB0cnkge1xuICAgIGxldCBqc29uYm9keTtcbiAgICBpZiAodHlwZW9mIHJlc3BvbnNlLmJvZHkgIT09ICdvYmplY3QnIHx8IEJ1ZmZlci5pc0J1ZmZlcihyZXNwb25zZS5ib2R5KSkge1xuICAgICAganNvbmJvZHkgPSBKU09OLnBhcnNlKHJlc3BvbnNlLmJvZHkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBqc29uYm9keSA9IHJlc3BvbnNlLmJvZHk7XG4gICAgfVxuICAgIHJlc3VsdCA9IHR5cGVvZiBqc29uYm9keS5lcnJvciA9PT0gJ3N0cmluZycgJiYganNvbmJvZHkuZXJyb3IubGVuZ3RoO1xuICAgIGlmIChyZXN1bHQpIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShyZXNwb25zZSwgJ2JvZHknLCB7IHZhbHVlOiBqc29uYm9keSwgY29uZmlndXJhYmxlOiB0cnVlIH0pO1xuICB9IGNhdGNoIChlcnIpIHt9XG5cbiAgcmV0dXJuIHJlc3VsdDtcbn07XG5cbmZ1bmN0aW9uIHByb2Nlc3NSZXNwb25zZShyZXNwb25zZSwgeyBzdGF0dXNDb2RlID0gMjAwLCBib2R5ID0gdHJ1ZSwgYmVhcmVyID0gZmFsc2UgfSA9IHt9KSB7XG4gIGlmIChyZXNwb25zZS5zdGF0dXNDb2RlICE9PSBzdGF0dXNDb2RlKSB7XG4gICAgaWYgKGJlYXJlcikge1xuICAgICAgdGhyb3dBdXRoZW50aWNhdGVFcnJvcnMocmVzcG9uc2UpO1xuICAgIH1cblxuICAgIGlmIChpc1N0YW5kYXJkQm9keUVycm9yKHJlc3BvbnNlKSkge1xuICAgICAgdGhyb3cgbmV3IE9QRXJyb3IocmVzcG9uc2UuYm9keSwgcmVzcG9uc2UpO1xuICAgIH1cblxuICAgIHRocm93IG5ldyBPUEVycm9yKFxuICAgICAge1xuICAgICAgICBlcnJvcjogZm9ybWF0KFxuICAgICAgICAgICdleHBlY3RlZCAlaSAlcywgZ290OiAlaSAlcycsXG4gICAgICAgICAgc3RhdHVzQ29kZSxcbiAgICAgICAgICBTVEFUVVNfQ09ERVNbc3RhdHVzQ29kZV0sXG4gICAgICAgICAgcmVzcG9uc2Uuc3RhdHVzQ29kZSxcbiAgICAgICAgICBTVEFUVVNfQ09ERVNbcmVzcG9uc2Uuc3RhdHVzQ29kZV0sXG4gICAgICAgICksXG4gICAgICB9LFxuICAgICAgcmVzcG9uc2UsXG4gICAgKTtcbiAgfVxuXG4gIGlmIChib2R5ICYmICFyZXNwb25zZS5ib2R5KSB7XG4gICAgdGhyb3cgbmV3IE9QRXJyb3IoXG4gICAgICB7XG4gICAgICAgIGVycm9yOiBmb3JtYXQoXG4gICAgICAgICAgJ2V4cGVjdGVkICVpICVzIHdpdGggYm9keSBidXQgbm8gYm9keSB3YXMgcmV0dXJuZWQnLFxuICAgICAgICAgIHN0YXR1c0NvZGUsXG4gICAgICAgICAgU1RBVFVTX0NPREVTW3N0YXR1c0NvZGVdLFxuICAgICAgICApLFxuICAgICAgfSxcbiAgICAgIHJlc3BvbnNlLFxuICAgICk7XG4gIH1cblxuICByZXR1cm4gcmVzcG9uc2UuYm9keTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBwcm9jZXNzUmVzcG9uc2U7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\n\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\n\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\n\nconst allowed = [\n  'agent',\n  'ca',\n  'cert',\n  'crl',\n  'headers',\n  'key',\n  'lookup',\n  'passphrase',\n  'pfx',\n  'timeout',\n];\n\nconst setDefaults = (props, options) => {\n  DEFAULT_HTTP_OPTIONS = defaultsDeep(\n    {},\n    props.length ? pick(options, ...props) : options,\n    DEFAULT_HTTP_OPTIONS,\n  );\n};\n\nsetDefaults([], {\n  headers: {\n    'User-Agent': `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n    'Accept-Encoding': 'identity',\n  },\n  timeout: 3500,\n});\n\nfunction send(req, body, contentType) {\n  if (contentType) {\n    req.removeHeader('content-type');\n    req.setHeader('content-type', contentType);\n  }\n  if (body) {\n    req.removeHeader('content-length');\n    req.setHeader('content-length', Buffer.byteLength(body));\n    req.write(body);\n  }\n  req.end();\n}\n\nconst nonces = new LRU({ max: 100 });\n\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n  let url;\n  try {\n    url = new URL(options.url);\n    delete options.url;\n    assert(/^(https?:)$/.test(url.protocol));\n  } catch (err) {\n    throw new TypeError('only valid absolute URLs can be requested');\n  }\n  const optsFn = this[HTTP_OPTIONS];\n  let opts = options;\n\n  const nonceKey = `${url.origin}${url.pathname}`;\n  if (DPoP && 'dpopProof' in this) {\n    opts.headers = opts.headers || {};\n    opts.headers.DPoP = await this.dpopProof(\n      {\n        htu: `${url.origin}${url.pathname}`,\n        htm: options.method || 'GET',\n        nonce: nonces.get(nonceKey),\n      },\n      DPoP,\n      accessToken,\n    );\n  }\n\n  let userOptions;\n  if (optsFn) {\n    userOptions = pick(\n      optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)),\n      ...allowed,\n    );\n  }\n  opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n\n  if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n    throw new TypeError('mutual-TLS certificate and key not set');\n  }\n\n  if (opts.searchParams) {\n    for (const [key, value] of Object.entries(opts.searchParams)) {\n      url.searchParams.delete(key);\n      url.searchParams.set(key, value);\n    }\n  }\n\n  let responseType;\n  let form;\n  let json;\n  let body;\n  ({ form, responseType, json, body, ...opts } = opts);\n\n  for (const [key, value] of Object.entries(opts.headers || {})) {\n    if (value === undefined) {\n      delete opts.headers[key];\n    }\n  }\n\n  let response;\n  const req = (url.protocol === 'https:' ? https.request : http.request)(url.href, opts);\n  return (async () => {\n    if (json) {\n      send(req, JSON.stringify(json), 'application/json');\n    } else if (form) {\n      send(req, querystring.stringify(form), 'application/x-www-form-urlencoded');\n    } else if (body) {\n      send(req, body);\n    } else {\n      send(req);\n    }\n\n    [response] = await Promise.race([once(req, 'response'), once(req, 'timeout')]);\n\n    // timeout reached\n    if (!response) {\n      req.destroy();\n      throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n    }\n\n    const parts = [];\n\n    for await (const part of response) {\n      parts.push(part);\n    }\n\n    if (parts.length) {\n      switch (responseType) {\n        case 'json': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              let value = Buffer.concat(parts);\n              try {\n                value = JSON.parse(value);\n              } catch (err) {\n                Object.defineProperty(err, 'response', { value: response });\n                throw err;\n              } finally {\n                Object.defineProperty(response, 'body', { value, configurable: true });\n              }\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        case undefined:\n        case 'buffer': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              const value = Buffer.concat(parts);\n              Object.defineProperty(response, 'body', { value, configurable: true });\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        default:\n          throw new TypeError('unsupported responseType request option');\n      }\n    }\n\n    return response;\n  })()\n    .catch((err) => {\n      if (response) Object.defineProperty(err, 'response', { value: response });\n      throw err;\n    })\n    .finally(() => {\n      const dpopNonce = response && response.headers['dpop-nonce'];\n      if (dpopNonce && NQCHAR.test(dpopNonce)) {\n        nonces.set(nonceKey, dpopNonce);\n      }\n    });\n};\n\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = () => Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsiRTpcXENvZGVcXFJlYWxodWItUmVwb3NcXG1yaC1wbGF0Zm9ybVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFx1bml4X3RpbWVzdGFtcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9ICgpID0+IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFBLHdCQUF3QiIsInNvdXJjZXMiOlsiRTpcXENvZGVcXFJlYWxodWItUmVwb3NcXG1yaC1wbGF0Zm9ybVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFx3ZWFrX2NhY2hlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzLmtleXN0b3JlcyA9IG5ldyBXZWFrTWFwKCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\n\nfunction hasScheme(input) {\n  if (input.includes('://')) return true;\n\n  const authority = input.replace(/(\\/|\\?)/g, '#').split('#')[0];\n  if (authority.includes(':')) {\n    const index = authority.indexOf(':');\n    const hostOrPort = authority.slice(index + 1);\n    if (!PORT.test(hostOrPort)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction acctSchemeAssumed(input) {\n  if (!input.includes('@')) return false;\n  const parts = input.split('@');\n  const host = parts[parts.length - 1];\n  return !(host.includes(':') || host.includes('/') || host.includes('?'));\n}\n\nfunction normalize(input) {\n  if (typeof input !== 'string') {\n    throw new TypeError('input must be a string');\n  }\n\n  let output;\n  if (hasScheme(input)) {\n    output = input;\n  } else if (acctSchemeAssumed(input)) {\n    output = `acct:${input}`;\n  } else {\n    output = `https://${input}`;\n  }\n\n  return output.split('#')[0];\n}\n\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\n\nmodule.exports = (wwwAuthenticate) => {\n  const params = {};\n  try {\n    while (REGEXP.exec(wwwAuthenticate) !== null) {\n      if (RegExp.$1 && RegExp.$2) {\n        params[RegExp.$1] = RegExp.$2.slice(1, -1);\n      }\n    }\n  } catch (err) {}\n\n  return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7QUFFSjtBQUNBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHd3d19hdXRoZW50aWNhdGVfcGFyc2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFJFR0VYUCA9IC8oXFx3Kyk9KFwiW15cIl0qXCIpL2c7XG5cbm1vZHVsZS5leHBvcnRzID0gKHd3d0F1dGhlbnRpY2F0ZSkgPT4ge1xuICBjb25zdCBwYXJhbXMgPSB7fTtcbiAgdHJ5IHtcbiAgICB3aGlsZSAoUkVHRVhQLmV4ZWMod3d3QXV0aGVudGljYXRlKSAhPT0gbnVsbCkge1xuICAgICAgaWYgKFJlZ0V4cC4kMSAmJiBSZWdFeHAuJDIpIHtcbiAgICAgICAgcGFyYW1zW1JlZ0V4cC4kMV0gPSBSZWdFeHAuJDIuc2xpY2UoMSwgLTEpO1xuICAgICAgfVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyKSB7fVxuXG4gIHJldHVybiBwYXJhbXM7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nmodule.exports = {\n  Issuer,\n  Strategy,\n  TokenSet,\n  errors: {\n    OPError,\n    RPError,\n  },\n  custom: {\n    setHttpOptionsDefaults: setDefaults,\n    http_options: HTTP_OPTIONS,\n    clock_tolerance: CLOCK_TOLERANCE,\n  },\n  generators,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxtQkFBTyxDQUFDLGtFQUFVO0FBQ2pDLFFBQVEsbUJBQW1CLEVBQUUsbUJBQU8sQ0FBQyxrRUFBVTtBQUMvQyxpQkFBaUIsbUJBQU8sQ0FBQyx3RkFBcUI7QUFDOUMsaUJBQWlCLG1CQUFPLENBQUMsd0VBQWE7QUFDdEMsUUFBUSxnQ0FBZ0MsRUFBRSxtQkFBTyxDQUFDLGtGQUFrQjtBQUNwRSxtQkFBbUIsbUJBQU8sQ0FBQywwRkFBc0I7QUFDakQsUUFBUSxjQUFjLEVBQUUsbUJBQU8sQ0FBQyxvRkFBbUI7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFxDb2RlXFxSZWFsaHViLVJlcG9zXFxtcmgtcGxhdGZvcm1cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgSXNzdWVyID0gcmVxdWlyZSgnLi9pc3N1ZXInKTtcbmNvbnN0IHsgT1BFcnJvciwgUlBFcnJvciB9ID0gcmVxdWlyZSgnLi9lcnJvcnMnKTtcbmNvbnN0IFN0cmF0ZWd5ID0gcmVxdWlyZSgnLi9wYXNzcG9ydF9zdHJhdGVneScpO1xuY29uc3QgVG9rZW5TZXQgPSByZXF1aXJlKCcuL3Rva2VuX3NldCcpO1xuY29uc3QgeyBDTE9DS19UT0xFUkFOQ0UsIEhUVFBfT1BUSU9OUyB9ID0gcmVxdWlyZSgnLi9oZWxwZXJzL2NvbnN0cycpO1xuY29uc3QgZ2VuZXJhdG9ycyA9IHJlcXVpcmUoJy4vaGVscGVycy9nZW5lcmF0b3JzJyk7XG5jb25zdCB7IHNldERlZmF1bHRzIH0gPSByZXF1aXJlKCcuL2hlbHBlcnMvcmVxdWVzdCcpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgSXNzdWVyLFxuICBTdHJhdGVneSxcbiAgVG9rZW5TZXQsXG4gIGVycm9yczoge1xuICAgIE9QRXJyb3IsXG4gICAgUlBFcnJvcixcbiAgfSxcbiAgY3VzdG9tOiB7XG4gICAgc2V0SHR0cE9wdGlvbnNEZWZhdWx0czogc2V0RGVmYXVsdHMsXG4gICAgaHR0cF9vcHRpb25zOiBIVFRQX09QVElPTlMsXG4gICAgY2xvY2tfdG9sZXJhbmNlOiBDTE9DS19UT0xFUkFOQ0UsXG4gIH0sXG4gIGdlbmVyYXRvcnMsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\n\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\n\nconst AAD_MULTITENANT_DISCOVERY = [\n  'https://login.microsoftonline.com/common/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration',\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n  claim_types_supported: ['normal'],\n  claims_parameter_supported: false,\n  grant_types_supported: ['authorization_code', 'implicit'],\n  request_parameter_supported: false,\n  request_uri_parameter_supported: true,\n  require_request_uri_registration: false,\n  response_modes_supported: ['query', 'fragment'],\n  token_endpoint_auth_methods_supported: ['client_secret_basic'],\n};\n\nclass Issuer {\n  #metadata;\n  constructor(meta = {}) {\n    const aadIssValidation = meta[AAD_MULTITENANT];\n    delete meta[AAD_MULTITENANT];\n    ['introspection', 'revocation'].forEach((endpoint) => {\n      // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n      // are defined\n      if (\n        meta[`${endpoint}_endpoint`] &&\n        meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined &&\n        meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined\n      ) {\n        if (meta.token_endpoint_auth_methods_supported) {\n          meta[`${endpoint}_endpoint_auth_methods_supported`] =\n            meta.token_endpoint_auth_methods_supported;\n        }\n        if (meta.token_endpoint_auth_signing_alg_values_supported) {\n          meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] =\n            meta.token_endpoint_auth_signing_alg_values_supported;\n        }\n      }\n    });\n\n    this.#metadata = new Map();\n\n    Object.entries(meta).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true,\n        });\n      }\n    });\n\n    registry.set(this.issuer, this);\n\n    const Client = getClient(this, aadIssValidation);\n\n    Object.defineProperties(this, {\n      Client: { value: Client, enumerable: true },\n      FAPI1Client: { value: class FAPI1Client extends Client {}, enumerable: true },\n      FAPI2Client: { value: class FAPI2Client extends Client {}, enumerable: true },\n    });\n  }\n\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n\n  static async webfinger(input) {\n    const resource = webfingerNormalize(input);\n    const { host } = url.parse(resource);\n    const webfingerUrl = `https://${host}/.well-known/webfinger`;\n\n    const response = await request.call(this, {\n      method: 'GET',\n      url: webfingerUrl,\n      responseType: 'json',\n      searchParams: { resource, rel: 'http://openid.net/specs/connect/1.0/issuer' },\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n\n    const location =\n      Array.isArray(body.links) &&\n      body.links.find(\n        (link) =>\n          typeof link === 'object' &&\n          link.rel === 'http://openid.net/specs/connect/1.0/issuer' &&\n          link.href,\n      );\n\n    if (!location) {\n      throw new RPError({\n        message: 'no issuer found in webfinger response',\n        body,\n      });\n    }\n\n    if (typeof location.href !== 'string' || !location.href.startsWith('https://')) {\n      throw new RPError({\n        printf: ['invalid issuer location %s', location.href],\n        body,\n      });\n    }\n\n    const expectedIssuer = location.href;\n    if (registry.has(expectedIssuer)) {\n      return registry.get(expectedIssuer);\n    }\n\n    const issuer = await this.discover(expectedIssuer);\n\n    if (issuer.issuer !== expectedIssuer) {\n      registry.del(issuer.issuer);\n      throw new RPError(\n        'discovered issuer mismatch, expected %s, got: %s',\n        expectedIssuer,\n        issuer.issuer,\n      );\n    }\n    return issuer;\n  }\n\n  static async discover(uri) {\n    const wellKnownUri = resolveWellKnownUri(uri);\n\n    const response = await request.call(this, {\n      method: 'GET',\n      responseType: 'json',\n      url: wellKnownUri,\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n    return new Issuer({\n      ...ISSUER_DEFAULTS,\n      ...body,\n      [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL) =>\n        wellKnownUri.startsWith(discoveryURL),\n      ),\n    });\n  }\n\n  async reloadJwksUri() {\n    await keystore.call(this, true);\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nfunction resolveWellKnownUri(uri) {\n  const parsed = url.parse(uri);\n  if (parsed.pathname.includes('/.well-known/')) {\n    return uri;\n  } else {\n    let pathname;\n    if (parsed.pathname.endsWith('/')) {\n      pathname = `${parsed.pathname}.well-known/openid-configuration`;\n    } else {\n      pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n    }\n    return url.format({ ...parsed, pathname });\n  }\n}\n\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\");\n\nmodule.exports = new LRU({ max: 100 });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiJBQUFBLFlBQVksbUJBQU8sQ0FBQywwREFBVzs7QUFFL0IsMkJBQTJCLFVBQVUiLCJzb3VyY2VzIjpbIkU6XFxDb2RlXFxSZWFsaHViLVJlcG9zXFxtcmgtcGxhdGZvcm1cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaXNzdWVyX3JlZ2lzdHJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IExSVSA9IHJlcXVpcmUoJ2xydS1jYWNoZScpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IG5ldyBMUlUoeyBtYXg6IDEwMCB9KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\n\nfunction verified(err, user, info = {}) {\n  if (err) {\n    this.error(err);\n  } else if (!user) {\n    this.fail(info);\n  } else {\n    this.success(user, info);\n  }\n}\n\nfunction OpenIDConnectStrategy(\n  { client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {},\n  verify,\n) {\n  if (!(client instanceof BaseClient)) {\n    throw new TypeError('client must be an instance of openid-client Client');\n  }\n\n  if (typeof verify !== 'function') {\n    throw new TypeError('verify callback must be a function');\n  }\n\n  if (!client.issuer || !client.issuer.issuer) {\n    throw new TypeError('client must have an issuer with an identifier');\n  }\n\n  this._client = client;\n  this._issuer = client.issuer;\n  this._verify = verify;\n  this._passReqToCallback = passReqToCallback;\n  this._usePKCE = usePKCE;\n  this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n  this._params = cloneDeep(params);\n\n  // state and nonce are handled in authenticate()\n  delete this._params.state;\n  delete this._params.nonce;\n\n  this._extras = cloneDeep(extras);\n\n  if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n  if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n  if (!this._params.scope) this._params.scope = 'openid';\n\n  if (this._usePKCE === true) {\n    const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported)\n      ? this._issuer.code_challenge_methods_supported\n      : false;\n\n    if (supportedMethods && supportedMethods.includes('S256')) {\n      this._usePKCE = 'S256';\n    } else if (supportedMethods && supportedMethods.includes('plain')) {\n      this._usePKCE = 'plain';\n    } else if (supportedMethods) {\n      throw new TypeError(\n        'neither code_challenge_method supported by the client is supported by the issuer',\n      );\n    } else {\n      this._usePKCE = 'S256';\n    }\n  } else if (typeof this._usePKCE === 'string' && !['plain', 'S256'].includes(this._usePKCE)) {\n    throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n  }\n\n  this.name = url.parse(client.issuer.issuer).hostname;\n}\n\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n  (async () => {\n    const client = this._client;\n    if (!req.session) {\n      throw new TypeError('authentication requires session support');\n    }\n    const reqParams = client.callbackParams(req);\n    const sessionKey = this._key;\n\n    const { 0: parameter, length } = Object.keys(reqParams);\n\n    /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */\n    if (length === 0 || (length === 1 && parameter === 'iss')) {\n      // provide options object with extra authentication parameters\n      const params = {\n        state: random(),\n        ...this._params,\n        ...options,\n      };\n\n      if (!params.nonce && params.response_type.includes('id_token')) {\n        params.nonce = random();\n      }\n\n      req.session[sessionKey] = pick(params, 'nonce', 'state', 'max_age', 'response_type');\n\n      if (this._usePKCE && params.response_type.includes('code')) {\n        const verifier = random();\n        req.session[sessionKey].code_verifier = verifier;\n\n        switch (this._usePKCE) {\n          case 'S256':\n            params.code_challenge = codeChallenge(verifier);\n            params.code_challenge_method = 'S256';\n            break;\n          case 'plain':\n            params.code_challenge = verifier;\n            break;\n        }\n      }\n\n      this.redirect(client.authorizationUrl(params));\n      return;\n    }\n    /* end authentication request */\n\n    /* start authentication response */\n\n    const session = req.session[sessionKey];\n    if (Object.keys(session || {}).length === 0) {\n      throw new Error(\n        format(\n          'did not find expected authorization request details in session, req.session[\"%s\"] is %j',\n          sessionKey,\n          session,\n        ),\n      );\n    }\n\n    const {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    } = session;\n\n    try {\n      delete req.session[sessionKey];\n    } catch (err) {}\n\n    const opts = {\n      redirect_uri: this._params.redirect_uri,\n      ...options,\n    };\n\n    const checks = {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    };\n\n    const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n\n    const passReq = this._passReqToCallback;\n    const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n\n    const args = [tokenset, verified.bind(this)];\n\n    if (loadUserinfo) {\n      if (!tokenset.access_token) {\n        throw new RPError({\n          message:\n            'expected access_token to be returned when asking for userinfo in verify callback',\n          tokenset,\n        });\n      }\n      const userinfo = await client.userinfo(tokenset);\n      args.splice(1, 0, userinfo);\n    }\n\n    if (passReq) {\n      args.unshift(req);\n    }\n\n    this._verify(...args);\n    /* end authentication response */\n  })().catch((error) => {\n    if (\n      (error instanceof OPError &&\n        error.error !== 'server_error' &&\n        !error.error.startsWith('invalid')) ||\n      error instanceof RPError\n    ) {\n      this.fail(error);\n    } else {\n      this.error(error);\n    }\n  });\n};\n\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass TokenSet {\n  constructor(values) {\n    Object.assign(this, values);\n    const { constructor, ...properties } = Object.getOwnPropertyDescriptors(\n      this.constructor.prototype,\n    );\n\n    Object.defineProperties(this, properties);\n  }\n\n  set expires_in(value) {\n    this.expires_at = now() + Number(value);\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  claims() {\n    if (!this.id_token) {\n      throw new TypeError('id_token not present in TokenSet');\n    }\n\n    return JSON.parse(base64url.decode(this.id_token.split('.')[1]));\n  }\n}\n\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvdG9rZW5fc2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLHdGQUFxQjtBQUMvQyxZQUFZLG1CQUFPLENBQUMsa0dBQTBCOztBQUU5QztBQUNBO0FBQ0E7QUFDQSxZQUFZLDZCQUE2QjtBQUN6QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUmVhbGh1Yi1SZXBvc1xcbXJoLXBsYXRmb3JtXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXHRva2VuX3NldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBiYXNlNjR1cmwgPSByZXF1aXJlKCcuL2hlbHBlcnMvYmFzZTY0dXJsJyk7XG5jb25zdCBub3cgPSByZXF1aXJlKCcuL2hlbHBlcnMvdW5peF90aW1lc3RhbXAnKTtcblxuY2xhc3MgVG9rZW5TZXQge1xuICBjb25zdHJ1Y3Rvcih2YWx1ZXMpIHtcbiAgICBPYmplY3QuYXNzaWduKHRoaXMsIHZhbHVlcyk7XG4gICAgY29uc3QgeyBjb25zdHJ1Y3RvciwgLi4ucHJvcGVydGllcyB9ID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcnMoXG4gICAgICB0aGlzLmNvbnN0cnVjdG9yLnByb3RvdHlwZSxcbiAgICApO1xuXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXModGhpcywgcHJvcGVydGllcyk7XG4gIH1cblxuICBzZXQgZXhwaXJlc19pbih2YWx1ZSkge1xuICAgIHRoaXMuZXhwaXJlc19hdCA9IG5vdygpICsgTnVtYmVyKHZhbHVlKTtcbiAgfVxuXG4gIGdldCBleHBpcmVzX2luKCkge1xuICAgIHJldHVybiBNYXRoLm1heC5hcHBseShudWxsLCBbdGhpcy5leHBpcmVzX2F0IC0gbm93KCksIDBdKTtcbiAgfVxuXG4gIGV4cGlyZWQoKSB7XG4gICAgcmV0dXJuIHRoaXMuZXhwaXJlc19pbiA9PT0gMDtcbiAgfVxuXG4gIGNsYWltcygpIHtcbiAgICBpZiAoIXRoaXMuaWRfdG9rZW4pIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2lkX3Rva2VuIG5vdCBwcmVzZW50IGluIFRva2VuU2V0Jyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIEpTT04ucGFyc2UoYmFzZTY0dXJsLmRlY29kZSh0aGlzLmlkX3Rva2VuLnNwbGl0KCcuJylbMV0pKTtcbiAgfVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IFRva2VuU2V0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;