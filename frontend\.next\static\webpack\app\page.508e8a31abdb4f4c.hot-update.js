"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/navigation/Header.tsx":
/*!**********************************************!*\
  !*** ./src/components/navigation/Header.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Header = ()=>{\n    var _session_user_name, _session_user, _session_user_email, _session_user1, _session_user2, _session_user_name1, _session_user3, _session_user_email1, _session_user4, _session_user5;\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isAuthDropdownOpen, setIsAuthDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const authDropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const navigationItems = [\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Developments',\n            href: '/developments'\n        },\n        {\n            name: 'Explore Places',\n            href: '/explore'\n        },\n        {\n            name: 'Properties',\n            href: '/properties'\n        },\n        {\n            name: 'Services',\n            href: '/services'\n        },\n        {\n            name: 'Blog',\n            href: '/blog'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    // Handle click outside to close dropdowns\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Header.useEffect.handleClickOutside\": (event)=>{\n                    if (authDropdownRef.current && !authDropdownRef.current.contains(event.target)) {\n                        setIsAuthDropdownOpen(false);\n                    }\n                    if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                        setIsUserMenuOpen(false);\n                    }\n                }\n            }[\"Header.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/images/logo.png\",\n                                    alt: \"MRH Platform Logo\",\n                                    width: 120,\n                                    height: 40,\n                                    className: \"h-8 lg:h-10 w-auto object-contain\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"bg-lime-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200\",\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                status === 'loading' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, undefined) : session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: userMenuRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                            className: \"flex items-center space-x-2 text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: ((_session_user = session.user) === null || _session_user === void 0 ? void 0 : (_session_user_name = _session_user.name) === null || _session_user_name === void 0 ? void 0 : _session_user_name.charAt(0)) || ((_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : (_session_user_email = _session_user1.email) === null || _session_user_email === void 0 ? void 0 : _session_user_email.charAt(0)) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: ((_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.name) || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/dashboard\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsUserMenuOpen(false),\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/profile\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsUserMenuOpen(false),\n                                                    children: \"Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                    className: \"my-2 border-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)();\n                                                        setIsUserMenuOpen(false);\n                                                    },\n                                                    className: \"block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    ref: authDropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsAuthDropdownOpen(!isAuthDropdownOpen),\n                                            className: \"flex items-center justify-center w-10 h-10 text-gray-700 hover:text-lime-600 hover:bg-gray-50 rounded-full transition-colors duration-200\",\n                                            \"aria-label\": \"User account menu\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isAuthDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/signin\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsAuthDropdownOpen(false),\n                                                    children: \"Sign in\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/signup\",\n                                                    className: \"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\",\n                                                    onClick: ()=>setIsAuthDropdownOpen(false),\n                                                    children: \"Create new account\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden border-t border-gray-100 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: item.href,\n                                    className: \"text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/list-property\",\n                                    className: \"block w-full bg-lime-500 text-white text-center px-6 py-3 rounded-lg font-semibold hover:bg-lime-600 transition-colors duration-200 mb-4\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"List My Property\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                session ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-lime-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: ((_session_user3 = session.user) === null || _session_user3 === void 0 ? void 0 : (_session_user_name1 = _session_user3.name) === null || _session_user_name1 === void 0 ? void 0 : _session_user_name1.charAt(0)) || ((_session_user4 = session.user) === null || _session_user4 === void 0 ? void 0 : (_session_user_email1 = _session_user4.email) === null || _session_user_email1 === void 0 ? void 0 : _session_user_email1.charAt(0)) || 'U'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: ((_session_user5 = session.user) === null || _session_user5 === void 0 ? void 0 : _session_user5.name) || 'User'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/profile\",\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)();\n                                                setIsMenuOpen(false);\n                                            },\n                                            className: \"block text-gray-700 hover:text-lime-600 transition-colors duration-200\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signin\",\n                                            className: \"block w-full text-gray-700 hover:text-lime-600 font-medium transition-colors duration-200 text-left\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signup\",\n                                            className: \"block w-full bg-gray-900 text-white text-center px-4 py-2 rounded-lg font-medium hover:bg-gray-800 transition-colors duration-200\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: \"Create new account\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Realhub-Repos\\\\mrh-platform\\\\frontend\\\\src\\\\components\\\\navigation\\\\Header.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"L7KWsbXvK3w4Wt4+DuA7UBkkSQ4=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession\n    ];\n});\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/Header.tsx\n"));

/***/ })

});