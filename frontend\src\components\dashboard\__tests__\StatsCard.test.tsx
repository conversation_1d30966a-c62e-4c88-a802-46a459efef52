import { render, screen } from '@testing-library/react'
import { HeartIcon } from '@heroicons/react/24/outline'
import StatsCard from '../StatsCard'

describe('StatsCard', () => {
  it('renders stats card with title and value', () => {
    render(
      <StatsCard
        title="Saved Properties"
        value={12}
        icon={<HeartIcon className="h-8 w-8" />}
        iconColor="text-red-500"
      />
    )

    expect(screen.getByText('Saved Properties')).toBeInTheDocument()
    expect(screen.getByText('12')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(
      <StatsCard
        title="Saved Properties"
        value={12}
        icon={<HeartIcon className="h-8 w-8" />}
        iconColor="text-red-500"
      />
    )

    const region = screen.getByRole('region')
    expect(region).toHaveAttribute('aria-label', 'Saved Properties statistics')
  })

  it('applies custom icon color', () => {
    const { container } = render(
      <StatsCard
        title="Test"
        value={5}
        icon={<HeartIcon className="h-8 w-8" />}
        iconColor="text-blue-500"
      />
    )

    const iconContainer = container.querySelector('.text-blue-500')
    expect(iconContainer).toBeInTheDocument()
  })
})
