import { PrismaClient } from '@prisma/client'
import logger from './logger.js'

// Check if we're in a serverless environment
const isServerless = !!(
  process.env.VERCEL ||
  process.env.AWS_LAMBDA_FUNCTION_NAME ||
  process.env.LAMBDA_TASK_ROOT ||
  process.env.VERCEL_ENV
)

// Global variable to store the Prisma client instance
let prisma = null

// Function to create and configure Prisma client
function createPrismaClient() {
  const client = new PrismaClient({
    log: isServerless
      ? [
          {
            emit: 'event',
            level: 'error'
          },
          {
            emit: 'event',
            level: 'warn'
          }
        ]
      : [
          {
            emit: 'event',
            level: 'query'
          },
          {
            emit: 'event',
            level: 'error'
          },
          {
            emit: 'event',
            level: 'info'
          },
          {
            emit: 'event',
            level: 'warn'
          }
        ]
  })

  // Log database queries in development only
  if (process.env.NODE_ENV === 'development' && !isServerless) {
    client.$on('query', e => {
      logger.debug(`Query: ${e.query}`)
      logger.debug(`Params: ${e.params}`)
      logger.debug(`Duration: ${e.duration}ms`)
    })
  }

  client.$on('error', e => {
    logger.error('Database error:', e)
  })

  if (!isServerless) {
    client.$on('info', e => {
      logger.info('Database info:', e)
    })
  }

  client.$on('warn', e => {
    logger.warn('Database warning:', e)
  })

  return client
}

// Lazy initialization function
function getPrismaClient() {
  if (!prisma) {
    prisma = createPrismaClient()
  }
  return prisma
}

// Graceful shutdown - only in non-serverless environments
if (!isServerless) {
  process.on('beforeExit', async () => {
    if (prisma) {
      await prisma.$disconnect()
    }
  })

  process.on('SIGTERM', async () => {
    if (prisma) {
      await prisma.$disconnect()
    }
  })

  process.on('SIGINT', async () => {
    if (prisma) {
      await prisma.$disconnect()
    }
  })
}

// Export the lazy-initialized client
export default getPrismaClient()
