import { body, param, query, validationResult } from 'express-validator'
import validator from 'validator'
import logger from '../utils/logger.js'

/**
 * Handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    logger.warn(`Validation failed for ${req.method} ${req.originalUrl}:`, errors.array())
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    })
  }
  next()
}

/**
 * Common validation rules
 */
const commonValidations = {
  // ID validation
  id: param('id').isString().isLength({ min: 1, max: 50 }).withMessage('ID must be a valid string'),

  // Email validation
  email: body('email').isEmail().normalizeEmail().isLength({ max: 255 }).withMessage('Must be a valid email address'),

  // Password validation
  password: body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be 8-128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage(
      'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'
    ),

  // Username validation
  username: body('username')
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens'),

  // Name validation
  firstName: body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('First name must be 1-50 characters and contain only letters, spaces, apostrophes, and hyphens'),

  lastName: body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Last name must be 1-50 characters and contain only letters, spaces, apostrophes, and hyphens'),

  // Description validation
  description: body('description')
    .optional()
    .isLength({ max: 500 })
    .trim()
    .withMessage('Description must be max 500 characters'),

  // Boolean validation
  isActive: body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),

  // Pagination validation
  page: query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Page must be a positive integer between 1 and 1000'),

  limit: query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be a positive integer between 1 and 100'),

  // Search validation
  search: query('search')
    .optional()
    .isLength({ max: 100 })
    .trim()
    .withMessage('Search term must be max 100 characters'),

  // Array validation
  arrayOfIds: fieldName =>
    body(fieldName)
      .isArray({ min: 1, max: 50 })
      .withMessage(`${fieldName} must be a non-empty array with max 50 items`)
      .custom(value => {
        if (!Array.isArray(value)) return false
        return value.every(id => typeof id === 'string' && id.length > 0 && id.length <= 50)
      })
      .withMessage(`All ${fieldName} must be valid non-empty strings`)
}

/**
 * Custom validators
 */
const customValidators = {
  // Check if value is not in blacklisted words
  notBlacklisted: value => {
    const blacklistedWords = ['admin', 'root', 'system', 'null', 'undefined']
    return !blacklistedWords.includes(value.toLowerCase())
  },

  // Check if string contains only safe characters
  safeString: value => {
    const dangerousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /data:text\/html/gi
    ]
    return !dangerousPatterns.some(pattern => pattern.test(value))
  },

  // Check if URL is safe
  safeUrl: value => {
    if (!validator.isURL(value)) return false
    const url = new URL(value)
    return ['http:', 'https:'].includes(url.protocol)
  },

  // Check if file extension is allowed
  allowedFileExtension: (filename, allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx']) => {
    const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'))
    return allowedExtensions.includes(ext)
  }
}

/**
 * Specific validation chains for different entities
 */
const validationChains = {
  // User validations
  createUser: [
    commonValidations.email,
    commonValidations.username,
    commonValidations.password,
    commonValidations.firstName,
    commonValidations.lastName,
    commonValidations.isActive,
    body('username').custom(customValidators.notBlacklisted).withMessage('Username contains restricted words'),
    body('email').custom(customValidators.safeString).withMessage('Email contains unsafe characters')
  ],

  updateUser: [
    commonValidations.id,
    body('email').optional().isEmail().normalizeEmail().isLength({ max: 255 }),
    body('username')
      .optional()
      .isLength({ min: 3, max: 30 })
      .matches(/^[a-zA-Z0-9_-]+$/),
    commonValidations.firstName,
    commonValidations.lastName,
    commonValidations.isActive,
    body('username')
      .optional()
      .custom(customValidators.notBlacklisted)
      .withMessage('Username contains restricted words')
  ],

  changePassword: [
    commonValidations.id,
    body('currentPassword').isLength({ min: 1 }).withMessage('Current password is required'),
    commonValidations.password
  ],

  // Group validations
  createGroup: [
    body('name')
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9\s_-]+$/)
      .withMessage(
        'Group name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
      ),
    commonValidations.description,
    commonValidations.isActive,
    body('name').custom(customValidators.notBlacklisted).withMessage('Group name contains restricted words')
  ],

  updateGroup: [
    commonValidations.id,
    body('name')
      .optional()
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9\s_-]+$/),
    commonValidations.description,
    commonValidations.isActive
  ],

  // Role validations
  createRole: [
    body('name')
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9\s_-]+$/)
      .withMessage(
        'Role name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
      ),
    commonValidations.description,
    commonValidations.isActive,
    body('name').custom(customValidators.notBlacklisted).withMessage('Role name contains restricted words')
  ],

  updateRole: [
    commonValidations.id,
    body('name')
      .optional()
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9\s_-]+$/),
    commonValidations.description,
    commonValidations.isActive
  ],

  // Module validations
  createModule: [
    body('name')
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9\s_-]+$/)
      .withMessage(
        'Module name must be 1-100 characters and contain only letters, numbers, spaces, underscores, and hyphens'
      ),
    commonValidations.description,
    commonValidations.isActive,
    body('name').custom(customValidators.notBlacklisted).withMessage('Module name contains restricted words')
  ],

  updateModule: [
    commonValidations.id,
    body('name')
      .optional()
      .isLength({ min: 1, max: 100 })
      .matches(/^[a-zA-Z0-9\s_-]+$/),
    commonValidations.description,
    commonValidations.isActive
  ],

  // Permission validations
  createPermission: [
    body('action')
      .isIn(['create', 'read', 'update', 'delete'])
      .withMessage('Action must be one of: create, read, update, delete'),
    body('moduleId').isString().isLength({ min: 1, max: 50 }).withMessage('Module ID is required'),
    commonValidations.description,
    commonValidations.isActive
  ],

  updatePermission: [
    commonValidations.id,
    body('action').optional().isIn(['create', 'read', 'update', 'delete']),
    commonValidations.description,
    commonValidations.isActive
  ],

  // Assignment validations
  assignToGroup: [param('groupId').isString().isLength({ min: 1, max: 50 }), commonValidations.arrayOfIds('userIds')],

  assignRolesToGroup: [
    param('groupId').isString().isLength({ min: 1, max: 50 }),
    commonValidations.arrayOfIds('roleIds')
  ],

  assignPermissionsToRole: [
    param('roleId').isString().isLength({ min: 1, max: 50 }),
    commonValidations.arrayOfIds('permissionIds')
  ],

  // Access control validations
  simulateAction: [
    body('userId').optional().isString().isLength({ min: 1, max: 50 }),
    body('module').isString().isLength({ min: 1, max: 100 }),
    body('action').isIn(['create', 'read', 'update', 'delete'])
  ],

  // Pagination and filtering
  listQuery: [
    commonValidations.page,
    commonValidations.limit,
    commonValidations.search,
    query('isActive').optional().isBoolean()
  ]
}

export { handleValidationErrors, commonValidations, customValidators, validationChains }
