# Vercel Deployment Guide

This guide will help you deploy your IAM Backend application to Vercel.

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Database**: Set up a PostgreSQL database (recommended: [Neon](https://neon.tech), [Supabase](https://supabase.com), or [PlanetScale](https://planetscale.com))
3. **Vercel CLI**: Already installed as a dev dependency

## Quick Deployment

### 1. Login to Vercel
```bash
npx vercel login
```

### 2. Deploy to Production
```bash
npm run deploy
```

### 3. Deploy Preview (for testing)
```bash
npm run deploy:preview
```

## Environment Variables Setup

You need to configure the following environment variables in your Vercel project:

### Required Environment Variables

1. **DATABASE_URL**
   - Your PostgreSQL connection string
   - Example: `****************************************/database?schema=public`

2. **JWT_SECRET**
   - A secure random string for JWT token signing
   - Generate with: `node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"`

3. **JWT_EXPIRES_IN**
   - Token expiration time (default: "24h")

4. **NODE_ENV**
   - Set to "production"

5. **RATE_LIMIT_WINDOW_MS**
   - Rate limiting window in milliseconds (default: 900000)

6. **RATE_LIMIT_MAX_REQUESTS**
   - Maximum requests per window (default: 100)

7. **LOG_LEVEL**
   - Logging level (default: "info")

### Setting Environment Variables

#### Option 1: Vercel Dashboard
1. Go to your project in [Vercel Dashboard](https://vercel.com/dashboard)
2. Navigate to Settings → Environment Variables
3. Add each variable with appropriate values

#### Option 2: Vercel CLI
```bash
# Set environment variables via CLI
vercel env add DATABASE_URL
vercel env add JWT_SECRET
vercel env add JWT_EXPIRES_IN
vercel env add NODE_ENV
vercel env add RATE_LIMIT_WINDOW_MS
vercel env add RATE_LIMIT_MAX_REQUESTS
vercel env add LOG_LEVEL
```

## Database Setup

### 1. Run Migrations
After setting up your database and environment variables:

```bash
# Generate Prisma client
npx prisma generate

# Deploy migrations to production database
npx prisma migrate deploy

# Seed the database (optional)
npx prisma db seed
```

### 2. Database Providers

#### Neon (Recommended)
- Free tier available
- Serverless PostgreSQL
- Easy setup and scaling

#### Supabase
- Free tier available
- PostgreSQL with additional features
- Built-in authentication (if needed)

#### PlanetScale
- MySQL-compatible
- Serverless database platform
- Branching for database schemas

## Deployment Configuration

The `vercel.json` file is already configured with:

- **Build**: Uses `@vercel/node` for Node.js applications
- **Routes**: All requests routed to `src/server.js`
- **Environment**: Production environment
- **Function Duration**: 30 seconds max
- **Region**: US East (iad1)

## Post-Deployment

### 1. Test Your Deployment
```bash
# Test health endpoint
curl https://your-app.vercel.app/health

# Test API endpoints
curl https://your-app.vercel.app/api/auth/health
```

### 2. Monitor Logs
- View logs in Vercel Dashboard → Functions tab
- Monitor performance and errors

### 3. Custom Domain (Optional)
1. Go to Vercel Dashboard → Settings → Domains
2. Add your custom domain
3. Configure DNS records as instructed

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify DATABASE_URL is correct
   - Ensure database is accessible from Vercel's IP ranges
   - Check if database allows external connections

2. **Environment Variables Not Working**
   - Redeploy after adding environment variables
   - Check variable names match exactly

3. **Function Timeout**
   - Optimize database queries
   - Consider increasing maxDuration in vercel.json (max 60s on Pro plan)

4. **Cold Start Issues**
   - Implement connection pooling
   - Consider using Vercel's Edge Runtime for faster cold starts

### Getting Help

- Check Vercel logs in the dashboard
- Review Prisma logs for database issues
- Use `vercel logs` command for recent logs

## Scripts Reference

- `npm run deploy` - Deploy to production
- `npm run deploy:preview` - Deploy preview version
- `npx vercel --help` - View all Vercel CLI commands
- `npx vercel logs` - View deployment logs
- `npx vercel env ls` - List environment variables
