'use client'

import { useState } from 'react'

interface SearchButtonProps {
  onClick: () => void
  loading?: boolean
  disabled?: boolean
  className?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary' | 'outline'
  fullWidth?: boolean
  children?: React.ReactNode
}

const SearchButton = ({
  onClick,
  loading = false,
  disabled = false,
  className = '',
  size = 'md',
  variant = 'primary',
  fullWidth = false,
  children
}: SearchButtonProps) => {
  const [isPressed, setIsPressed] = useState(false)

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  }

  const variantClasses = {
    primary: 'bg-lime-500 text-white hover:bg-lime-600 focus:ring-lime-200 shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-900 text-white hover:bg-gray-800 focus:ring-gray-200 shadow-lg hover:shadow-xl',
    outline: 'border-2 border-lime-500 text-lime-600 hover:bg-lime-500 hover:text-white focus:ring-lime-200'
  }

  const baseClasses = `
    font-semibold rounded-lg transition-all duration-200 transform
    focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed
    flex items-center justify-center gap-2
    ${fullWidth ? 'w-full' : ''}
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${isPressed ? 'scale-95' : 'hover:scale-105'}
    ${loading ? 'cursor-wait' : ''}
    ${className}
  `

  const handleClick = () => {
    if (!disabled && !loading) {
      onClick()
    }
  }

  const handleMouseDown = () => setIsPressed(true)
  const handleMouseUp = () => setIsPressed(false)
  const handleMouseLeave = () => setIsPressed(false)

  return (
    <button
      type="button"
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      disabled={disabled || loading}
      className={baseClasses}
    >
      {loading ? (
        <>
          <svg className="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <span>Searching...</span>
        </>
      ) : (
        <>
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <span>{children || 'Find Properties'}</span>
        </>
      )}
    </button>
  )
}

export default SearchButton
